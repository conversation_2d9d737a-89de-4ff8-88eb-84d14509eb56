<view class="container">
  <view class="header">
    <text class="title">我的邀请码</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>

  <!-- 邀请码展示区域 -->
  <view class="invite-code-container" wx:else>
    <view class="invite-code-box">
      <view class="invite-code-header">
        <text class="invite-code-title">我的专属邀请码</text>
        <text class="invite-code-expire" wx:if="{{expireTime}}">有效期至：{{expireTime}}（剩余{{expireDays}}天）</text>
      </view>
      <view class="invite-code">{{inviteCode || '暂无邀请码'}}</view>
      <view class="invite-code-actions">
        <button class="action-btn copy-btn" bindtap="copyInviteCode" wx:if="{{inviteCode}}">复制邀请码</button>
        <button class="action-btn share-btn" bindtap="showShareDialog" wx:if="{{inviteCode}}">分享邀请码</button>
        <button class="action-btn generate-btn" bindtap="generateInviteCode" wx:if="{{!inviteCode}}">生成邀请码</button>
      </view>
    </view>

    <!-- 邀请统计 -->
    <view class="stats-container">
      <view class="stats-item">
        <text class="stats-value">{{inviteCount}}</text>
        <text class="stats-label">累计邀请</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{totalPointsEarned}}</text>
        <text class="stats-label">获得积分</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{dailyRemaining}}</text>
        <text class="stats-label">今日剩余奖励次数</text>
      </view>
    </view>

    <!-- 邀请规则 -->
    <view class="rules-container">
      <view class="section-title">邀请规则</view>
      <view class="rules-content">
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">每成功邀请1名新用户注册，可获得10积分奖励</text>
        </view>
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">每日最多可通过邀请获得10次奖励（今日剩余：{{dailyRemaining}}次）</text>
        </view>
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">邀请码有效期为6个月，过期后需重新生成</text>
        </view>
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">每个新用户只能被邀请一次</text>
        </view>
      </view>
    </view>

    <!-- 活动区域 -->
    <view class="activities-container" wx:if="{{activeActivities.length > 0}}">
      <view class="section-title">邀请活动</view>
      <view class="activities-list">
        <view class="activity-item" wx:for="{{activeActivities}}" wx:key="_id">
          <view class="activity-name">{{item.name}}</view>
          <view class="activity-desc">{{item.description}}</view>
          <view class="activity-time">{{item.start_time}} ~ {{item.end_time}}</view>
          <view class="activity-bonus">奖励倍数：×{{item.points_multiplier}}</view>
        </view>
      </view>
    </view>

    <!-- 邀请排行榜入口 -->
    <view class="ranking-entry" bindtap="goToRanking">
      <text>查看邀请排行榜</text>
      <text class="arrow">></text>
    </view>

    <!-- 已邀请用户列表 -->
    <view class="invited-users-container" wx:if="{{invitedUsers.length > 0}}">
      <view class="section-title">已邀请用户</view>
      <view class="invited-users-list">
        <view class="invited-user-item" wx:for="{{invitedUsers}}" wx:key="_id">
          <image class="user-avatar" src="{{item.avatarUrl || '/static/images/default-avatar.png'}}"></image>
          <view class="user-info">
            <text class="user-nickname">{{item.nickname || '用户' + item._id.substring(0, 4)}}</text>
            <text class="invite-time">注册时间：{{item.register_time}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="no-data" wx:else>
      <text>暂无邀请记录</text>
    </view>
  </view>

  <!-- 分享弹窗 -->
  <view class="share-dialog-mask" wx:if="{{shareDialogVisible}}" bindtap="closeShareDialog">
    <view class="share-dialog" catchtap>
      <view class="share-dialog-header">
        <text class="share-dialog-title">分享邀请码</text>
        <text class="share-dialog-close" bindtap="closeShareDialog">×</text>
      </view>
      <view class="share-dialog-content">
        <view class="share-code">
          <text>您的邀请码：</text>
          <text class="code-text">{{inviteCode}}</text>
        </view>
        <view class="share-qrcode" wx:if="{{shareImage}}">
          <image src="{{shareImage}}" mode="aspectFit"></image>
        </view>
        <view class="share-link">
          <text>分享链接：</text>
          <text class="link-text">{{shareLink}}</text>
        </view>
        <view class="share-actions">
          <button class="share-btn" bindtap="saveShareImage">保存图片</button>
          <button class="share-btn" open-type="share">分享给朋友</button>
        </view>
      </view>
    </view>
  </view>
</view> 