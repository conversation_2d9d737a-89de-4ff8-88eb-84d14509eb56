// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 生成随机邀请码
function generateRandomCode(length = 8) {
  // 定义包含字母和数字的字符集（排除容易混淆的字符如0和O、1和l等）
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
  let result = ''
  
  // 生成指定长度的随机字符串
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length)
    result += chars.charAt(randomIndex)
  }
  
  return result
}

// 检查邀请码是否已存在
async function isCodeExists(code) {
  const result = await db.collection('invitation_codes')
    .where({
      code: code,
      status: 'active'
    })
    .count()
  
  return result.total > 0
}

// 生成唯一邀请码
async function generateUniqueCode() {
  let code
  let exists
  let attempts = 0
  const maxAttempts = 10 // 最大尝试次数
  
  do {
    code = generateRandomCode()
    exists = await isCodeExists(code)
    attempts++
  } while (exists && attempts < maxAttempts)
  
  if (exists) {
    throw new Error('无法生成唯一邀请码，请稍后重试')
  }
  
  return code
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userId = event.userId || wxContext.OPENID
  
  if (!userId) {
    return {
      success: false,
      message: '用户ID不能为空'
    }
  }
  
  try {
    // 检查用户是否已有有效邀请码
    const existingCodeResult = await db.collection('invitation_codes')
      .where({
        user_id: userId,
        status: 'active',
        expire_time: _.gt(new Date())
      })
      .limit(1)
      .get()
    
    // 如果已有有效邀请码，直接返回
    if (existingCodeResult.data.length > 0) {
      const existingCode = existingCodeResult.data[0]
      return {
        success: true,
        code: existingCode.code,
        expireTime: existingCode.expire_time,
        message: '已有有效邀请码'
      }
    }
    
    // 生成新的邀请码
    const code = await generateUniqueCode()
    
    // 设置过期时间（6个月后）
    const now = new Date()
    const expireTime = new Date(now)
    expireTime.setMonth(now.getMonth() + 6)
    
    // 保存到数据库
    const result = await db.collection('invitation_codes').add({
      data: {
        user_id: userId,
        code: code,
        create_time: now,
        expire_time: expireTime,
        status: 'active',
        usage_count: 0
      }
    })
    
    return {
      success: true,
      code: code,
      expireTime: expireTime,
      message: '邀请码生成成功'
    }
  } catch (error) {
    console.error('生成邀请码异常：', error)
    return {
      success: false,
      message: '生成邀请码失败：' + error.message
    }
  }
}