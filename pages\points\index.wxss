/* pages/points/index.wxss */
.points-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-color: #f4f4f8; /* 淡雅的背景色 */
  padding-bottom: 40rpx; /* 底部留一些空间 */
}

/* 用户信息与总积分区域 */
.user-profile-section {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 渐变背景 */
  color: #ffffff;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.user-info-box {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 25rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
}

.total-points-box {
  text-align: center;
}

.points-label {
  font-size: 28rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
  display: block;
}

.points-value {
  font-size: 64rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
}

/* 加载指示器 */
.loading-indicator,
.loading-more-indicator,
.no-more-data-indicator,
.empty-state,
.error-message-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #888;
  font-size: 28rpx;
  width: 100%;
}

.loading-gif {
  width: 100rpx; /* 调整GIF大小 */
  height: 100rpx;
  margin-bottom: 15rpx;
}

.error-icon, .empty-icon {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}

.retry-button {
  margin-top: 30rpx;
  background-color: #667eea;
  color: white;
  padding: 15rpx 40rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
}

/* 积分记录区域 */
.points-records-section {
  width: 92%;
  margin-top: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 0 0 20rpx 0; /* 底部padding给加载提示留空间 */
}

/* 原有的records-header样式保留，但添加新的可折叠标题样式 */
.records-header {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

/* 可折叠的积分明细标题 */
.records-header-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.records-header-toggle:active {
  background-color: #f9f9f9;
}

.records-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.records-toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.records-toggle-icon.expanded {
  transform: rotate(180deg);
}

/* 可折叠的积分明细内容 */
.records-content {
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
}

.records-content.hide {
  max-height: 0;
  opacity: 0;
}

.records-content.show {
  max-height: 2000rpx; /* 设置一个足够大的值 */
  opacity: 1;
}

.records-scroll-view {
  max-height: calc(100vh - 450rpx); /* 估算一个最大高度，确保可滚动 */
  /* 实际高度可能需要根据 user-profile-section 的动态高度调整，或设一个较大的固定值 */
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:active {
  background-color: #f9f9f9;
}

.record-left {
  flex: 1;
}

.record-description {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
  word-break: break-all;
}

.record-action-type {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #aaa;
}

.record-right {
  margin-left: 20rpx;
}

.points-change {
  font-size: 34rpx;
  font-weight: 500;
}

.points-positive {
  color: #4CAF50; /* 绿色代表增加 */
}

.points-negative {
  color: #F44336; /* 红色代表减少 */
}

/* 无数据或加载更多提示的特定样式 */
.no-more-data-indicator {
  padding: 30rpx 0;
  color: #bbb;
}

.empty-state text {
  font-size: 30rpx;
  color: #aaa;
}

/* 测试按钮区域 */
.mock-buttons-section {
  width: 92%;
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.mock-buttons-section .wx-button {
  margin: 10rpx !important;
}

/* 添加CSS加载动画 */
.loading-spinner {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #007aff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 添加积分操作按钮的样式 */
.points-operation-btn {
  margin-top: 20rpx;
  background-color: #ffffff;
  color: #667eea;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 2rpx solid #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 积分说明区域样式 */
.points-explanation-section {
  width: 100%;
  margin-top: 20rpx;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  overflow: hidden;
}

.explanation-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 25rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.explanation-toggle:active {
  background-color: rgba(255, 255, 255, 0.1);
}

.explanation-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
}

.toggle-icon {
  font-size: 24rpx;
  color: #ffffff;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.explanation-content {
  overflow: hidden;
  transition: max-height 0.3s ease, opacity 0.3s ease;
}

.explanation-content.hide {
  max-height: 0;
  opacity: 0;
}

.explanation-content.show {
  max-height: 1000rpx;
  opacity: 1;
}

.explanation-category {
  padding: 20rpx 25rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.explanation-category:last-child {
  border-bottom: none;
}

.category-title {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.category-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.category-text {
  font-size: 26rpx;
  font-weight: 500;
  color: #ffffff;
}

.rule-list {
  margin-left: 42rpx;
}

.rule-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 0;
  flex-wrap: wrap;
}

.rule-action {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
  min-width: 200rpx;
}

.rule-points {
  font-size: 24rpx;
  font-weight: 500;
  margin-left: 10rpx;
}

.rule-points.gain {
  color: #4CAF50;
}

.rule-points.cost {
  color: #FF6B6B;
}

.rule-limit {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 8rpx;
}

.explanation-tips {
  padding: 20rpx 25rpx;
  background-color: rgba(255, 255, 255, 0.05);
}

.tips-title {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #FFD700;
  margin-bottom: 10rpx;
}

.tips-content {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 5rpx;
}