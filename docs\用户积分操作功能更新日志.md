# 用户积分操作功能更新日志

## 2023-08-15 更新：用户积分操作页面

### 功能概述

为了方便管理员对指定用户进行积分操作，我们新增了一个专门的用户积分操作页面。该页面允许管理员选择不同的积分操作类型，并根据选择的类型对用户进行积分增减操作。

### 主要更新内容

1. 创建了新的用户积分操作页面：
   - 路径：`pages/admin/user-points-operation/`
   - 文件：`index.js`, `index.wxml`, `index.wxss`, `index.json`

2. 实现的功能：
   - 显示用户基本信息（头像、昵称、ID、当前积分）
   - 提供积分操作类型选择器，包含以下类型：
     - 管理员自定义调整
     - 新用户注册奖励 (+20分)
     - 推荐新用户奖励 (+10分)
     - 提交普通停车场奖励 (+10分)
     - 提交优质停车场奖励 (+20分)
     - 审核通过额外奖励 (+2分)
     - 评论普通停车场奖励 (+3分)
     - 评论优质停车场奖励 (+5分)
     - 查看普通停车场扣除 (-15分)
     - 查看优质停车场扣除 (-30分)
   - 根据选择的操作类型自动设置积分值，或在自定义调整模式下允许输入积分值
   - 提供操作原因输入框
   - 提交操作到云函数 `operateUserPoints`，实现积分调整并记录日志

3. 修改用户管理页面的跳转逻辑：
   - 将用户项的点击事件从跳转到用户详情页改为跳转到新的积分操作页面
   - 文件：`pages/admin/userManagement/index.js`

4. 在 `app.json` 中注册新页面

### 技术实现细节

1. 积分操作类型定义：
   - 在页面数据中定义了一个操作类型列表，包含各种积分操作类型及其对应的积分值
   - 每种操作类型都有唯一的ID、显示名称、积分值和对应的云函数action类型

2. 云函数调用参数：
   - userId：用户ID
   - pointsChange：积分变化值（正数增加，负数减少）
   - action：操作类型（如 'ADMIN_ADD_POINTS', 'ADMIN_DEDUCT_POINTS'）
   - description：积分变化的具体描述，包含操作类型名称、操作原因和积分变化值

3. 用户体验优化：
   - 添加了详细的错误提示和操作反馈
   - 操作成功后自动更新显示的用户积分
   - 清空输入框，方便进行下一次操作
   - 添加了积分规则说明，方便管理员了解各种积分操作的规则

### 后续优化计划

1. 添加积分操作历史记录查看功能，方便管理员查看对特定用户的历史操作
2. 优化操作类型选择器的交互体验，可考虑使用更直观的分类展示
3. 添加批量积分操作功能，方便管理员对多个用户同时进行相同的积分操作
4. 完善权限控制，确保只有管理员可以进行积分操作 

## 2023-11-15 更新：邀请码系统积分操作

### 功能概述

为了支持新增的邀请码系统，我们对用户积分操作功能进行了扩展，添加了与邀请码相关的积分操作类型，并优化了积分操作的处理逻辑。

### 主要更新内容

1. 在用户积分操作页面添加邀请码相关的积分操作类型：
   - 邀请新用户注册奖励 (+10分)
   - 被邀请注册额外奖励 (+5分)
   - 邀请排行榜奖励 (+50分)

2. 扩展 `operateUserPoints` 云函数：
   - 添加对 `invite_reward` 操作类型的支持
   - 添加对邀请相关积分记录的处理逻辑
   - 增强防刷机制，限制每日邀请奖励次数

3. 修改积分记录展示页面：
   - 添加对邀请相关积分记录的特殊显示样式
   - 在积分记录中显示邀请人或被邀请人的信息

4. 在用户个人中心添加邀请入口：
   - 添加"邀请好友"按钮，跳转到邀请码页面
   - 显示已邀请人数和获得的邀请积分总数

### 技术实现细节

1. 新增积分操作类型定义：
```javascript
{
  id: 'invite_reward',
  name: '邀请新用户注册奖励',
  points: 10,
  action: 'invite_reward',
  description: '邀请新用户注册成功'
},
{
  id: 'invited_bonus',
  name: '被邀请注册额外奖励',
  points: 5,
  action: 'invited_bonus',
  description: '使用邀请码注册的额外奖励'
},
{
  id: 'invite_rank_reward',
  name: '邀请排行榜奖励',
  points: 50,
  action: 'invite_rank_reward',
  description: '邀请排行榜前10名奖励'
}
```

2. 邀请奖励限制逻辑：
```javascript
// 检查每日邀请奖励限制
if (action === 'invite_reward') {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  const dailyInviteRewards = await db.collection('point_logs').where({
    user_id: userId,
    action: 'invite_reward',
    create_time: _.gte(today).and(_.lt(tomorrow))
  }).count();
  
  // 每日最多获得5次邀请奖励
  if (dailyInviteRewards.total >= 5) {
    return {
      success: false,
      code: 'DAILY_LIMIT_EXCEEDED',
      message: '已达到每日邀请奖励上限'
    };
  }
}
```

3. 积分记录展示优化：
```javascript
// 根据积分记录类型设置不同的显示样式
function formatPointRecord(record) {
  // 处理原有积分记录类型
  // ...
  
  // 处理邀请相关积分记录
  if (record.action === 'invite_reward') {
    record.icon = '/assets/icons/invite.png';
    record.color = '#FF9500';
    record.actionText = '邀请奖励';
  } else if (record.action === 'invited_bonus') {
    record.icon = '/assets/icons/gift.png';
    record.color = '#FF9500';
    record.actionText = '邀请注册奖励';
  } else if (record.action === 'invite_rank_reward') {
    record.icon = '/assets/icons/crown.png';
    record.color = '#FF9500';
    record.actionText = '排行榜奖励';
  }
  
  return record;
}
```

### 后续优化计划

1. 添加邀请统计数据可视化展示，如邀请转化率图表
2. 实现多级邀请奖励机制，奖励邀请链上的所有用户
3. 开发特定时期的邀请活动功能，提高邀请奖励以促进用户增长
4. 优化邀请积分记录的展示，添加更多详细信息和交互功能
5. 实现邀请排行榜自动奖励发放功能 