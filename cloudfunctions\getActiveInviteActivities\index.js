// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const now = new Date()
    
    // 查询当前活跃的活动
    const activitiesResult = await db.collection('invitation_activities')
      .where({
        status: 'active',
        start_time: _.lte(now),
        end_time: _.gte(now)
      })
      .orderBy('points_multiplier', 'desc')
      .get()
    
    // 格式化活动数据
    const activities = activitiesResult.data.map(activity => {
      return {
        _id: activity._id,
        name: activity.name,
        description: activity.description,
        start_time: activity.start_time.toISOString().split('T')[0],
        end_time: activity.end_time.toISOString().split('T')[0],
        points_multiplier: activity.points_multiplier
      }
    })
    
    return {
      success: true,
      activities: activities
    }
  } catch (error) {
    console.error('获取活跃邀请活动异常：', error)
    return {
      success: false,
      message: '获取活动数据失败：' + error.message
    }
  }
} 