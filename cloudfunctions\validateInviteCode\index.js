// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { code } = event
  
  if (!code) {
    return {
      valid: false,
      message: '邀请码不能为空'
    }
  }
  
  try {
    // 查询邀请码
    const codeResult = await db.collection('invitation_codes')
      .where({
        code: code,
        status: 'active',
        expire_time: _.gt(new Date()) // 确保邀请码未过期
      })
      .get()
    
    // 邀请码不存在或已过期
    if (codeResult.data.length === 0) {
      return {
        valid: false,
        message: '邀请码无效或已过期'
      }
    }
    
    const inviteCode = codeResult.data[0]
    const inviterId = inviteCode.user_id
    
    // 检查是否自己邀请自己
    if (inviterId === wxContext.OPENID) {
      return {
        valid: false,
        message: '不能使用自己的邀请码'
      }
    }
    
    // 获取邀请人信息
    const inviterResult = await db.collection('users')
      .doc(inviterId)
      .field({
        nickname: true,
        username: true,
        avatarUrl: true
      })
      .get()
      .catch(err => {
        console.error('获取邀请人信息失败：', err)
        return { data: {} }
      })
    
    return {
      valid: true,
      inviterId: inviterId,
      inviterInfo: inviterResult.data || {},
      message: '邀请码有效'
    }
  } catch (error) {
    console.error('验证邀请码异常：', error)
    return {
      valid: false,
      message: '验证邀请码失败：' + error.message
    }
  }
} 