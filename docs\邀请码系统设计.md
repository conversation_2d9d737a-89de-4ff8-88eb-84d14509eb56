# 邀请码系统设计文档

## 一、系统概述

邀请码系统是作为一个独立模块设计的，与现有用户系统完全解耦，不需要修改现有的users表结构。该系统允许用户生成专属邀请码并通过多种方式分享（粘贴邀请码或分享链接），当新用户通过邀请注册后，邀请人可获得积分奖励。系统设置了防刷机制，限制每个用户每天最多邀请10人获得奖励，确保邀请行为的真实性和有效性。

## 二、数据结构设计

### 1. 邀请码表（`invitation_codes`）
存储用户的邀请码信息，与用户表解耦。

| 字段名 | 类型 | 描述 |
|-------|------|------|
| _id | String | 记录ID（主键） |
| user_id | String | 用户ID（关联users表） |
| code | String | 邀请码 |
| create_time | DateTime | 创建时间 |
| expire_time | DateTime | 过期时间 |
| status | String | 状态（active/inactive） |
| usage_count | Integer | 使用次数 |

### 2. 邀请记录表（`invitation_records`）
记录邀请关系和奖励情况。

| 字段名 | 类型 | 描述 |
|-------|------|------|
| _id | String | 记录ID（主键） |
| inviter_id | String | 邀请人ID |
| invitee_id | String | 被邀请人ID |
| code | String | 使用的邀请码 |
| create_time | DateTime | 邀请时间 |
| status | String | 状态（pending/completed） |
| points_awarded | Integer | 奖励的积分数量 |
| activity_id | String | 活动ID（关联特定活动，可选） |

### 3. 邀请活动表（`invitation_activities`）
存储特定时期的邀请活动信息。

| 字段名 | 类型 | 描述 |
|-------|------|------|
| _id | String | 活动ID（主键） |
| name | String | 活动名称 |
| description | String | 活动描述 |
| start_time | DateTime | 开始时间 |
| end_time | DateTime | 结束时间 |
| points_multiplier | Float | 积分倍数 |
| status | String | 状态（upcoming/active/ended） |

## 三、功能设计

### 1. 邀请码生成机制

1. **生成规则**：
   - 用户首次使用邀请功能时自动生成
   - 长度为8位字符（字母+数字组合）
   - 确保系统内唯一性
   - 基于用户ID和随机字符组合

2. **有效期设置**：
   - 默认有效期为6个月
   - 过期后自动失效，需重新生成
   - 系统定期清理过期邀请码

### 2. 邀请流程

1. **邀请发起**：
   - 用户在个人中心查看自己的邀请码
   - 提供一键复制邀请码功能
   - 生成包含邀请码的分享链接
   - 生成包含邀请码的小程序码

2. **邀请接收**：
   - 新用户通过分享链接进入小程序，自动填写邀请码
   - 或在注册页面手动输入邀请码
   - 系统验证邀请码有效性

3. **邀请完成**：
   - 新用户完成注册后，系统记录邀请关系
   - 更新邀请统计数据
   - 向邀请人发放积分奖励

### 3. 积分奖励机制

1. **奖励规则**：
   - 基础奖励：每成功邀请一名新用户，邀请人获得10积分
   - 每日上限：每天最多通过邀请获得积分的次数为10次
   - 只有直接邀请才能获得奖励，不设置多级奖励机制

2. **活动奖励**：
   - 特定活动期间，邀请奖励可提高（如双倍积分）
   - 活动规则可在后台灵活配置
   - 活动期间邀请显示特殊标识

### 4. 邀请统计与展示

1. **个人邀请数据**：
   - 在个人中心展示累计邀请人数
   - 展示通过邀请获得的总积分
   - 展示已邀请用户列表（包含头像、昵称、注册时间）
   - 展示邀请码有效期

2. **邀请排行榜**：
   - 设置邀请排行榜，展示邀请人数最多的用户
   - 分为日榜、周榜、月榜和总榜
   - 排行榜前列用户获得额外奖励
   - 展示用户头像、昵称、邀请人数和排名

## 四、接口设计

### 1. 云函数 `generateInviteCode`

- **功能**：为用户生成唯一邀请码
- **输入参数**：`userId`
- **返回值**：`{success: Boolean, code: String, expireTime: DateTime}`
- **调用时机**：用户首次访问邀请页面或邀请码过期时

### 2. 云函数 `validateInviteCode`

- **功能**：验证邀请码有效性
- **输入参数**：`code`
- **返回值**：`{valid: Boolean, inviterId: String, message: String}`
- **调用时机**：新用户注册时输入邀请码后调用

### 3. 云函数 `completeInvitation`

- **功能**：完成邀请流程，记录邀请关系，发放奖励
- **输入参数**：`inviterId`, `inviteeId`, `code`
- **返回值**：`{success: Boolean, pointsAwarded: Number, message: String}`
- **调用时机**：新用户注册成功后调用

### 4. 云函数 `getUserInviteStats`

- **功能**：获取用户的邀请统计数据
- **输入参数**：`userId`
- **返回值**：`{code: String, expireTime: DateTime, inviteCount: Number, totalPointsEarned: Number, invitedUsers: Array}`
- **调用时机**：用户查看个人邀请数据时调用

### 5. 云函数 `getInviteRanking`

- **功能**：获取邀请排行榜数据
- **输入参数**：`type` (日榜/周榜/月榜/总榜), `limit` (返回数量)
- **返回值**：`{rankings: Array, userRank: Object}`
- **调用时机**：用户查看排行榜时调用

### 6. 云函数 `getActiveInviteActivities`

- **功能**：获取当前活跃的邀请活动
- **输入参数**：无
- **返回值**：`{activities: Array}`
- **调用时机**：用户访问邀请页面时调用

## 五、前端页面设计

### 1. 邀请码展示页面 (`pages/invite/index`)

- 展示用户的专属邀请码及有效期
- 提供一键复制邀请码功能
- 生成并展示分享链接和小程序码
- 提供分享按钮
- 展示邀请奖励规则说明
- 展示当前活跃的邀请活动
- 展示已邀请用户列表

### 2. 注册页面扩展 (`pages/register/index`)

- 添加邀请码输入框
- 支持从URL参数自动填写邀请码
- 提供邀请码验证功能
- 展示邀请人信息（可选）

### 3. 邀请排行榜页面 (`pages/invite/ranking`)

- 展示邀请人数排行榜
- 提供日榜/周榜/月榜/总榜切换
- 显示用户头像、昵称、邀请人数
- 突出显示当前用户排名
- 展示排行榜奖励规则

### 4. 邀请活动页面 (`pages/invite/activity`)

- 展示当前和即将开始的邀请活动
- 显示活动时间、奖励倍数等信息
- 提供活动规则说明
- 展示活动倒计时

## 六、安全性考虑

1. **防刷机制**：
   - 每个用户每天最多邀请10人获得奖励
   - 同一设备/IP短时间内注册多个账号时进行风控
   - 监控异常邀请行为，如短时间内大量成功邀请

2. **邀请码安全**：
   - 避免使用连续数字或易猜测的规律
   - 邀请码生成加入随机因素
   - 设置有效期，定期更新邀请码

3. **数据一致性**：
   - 使用数据库事务确保邀请记录和积分奖励的一致性
   - 防止重复奖励发放
   - 定期清理无效邀请记录

## 七、实现步骤

1. **数据库准备**：
   - 创建邀请码表、邀请记录表和邀请活动表
   - 设置必要的索引提高查询效率

2. **后端开发**：
   - 实现邀请码生成算法
   - 开发邀请验证和完成邀请的云函数
   - 集成现有积分系统，实现奖励发放
   - 实现邀请排行榜统计逻辑

3. **前端开发**：
   - 开发邀请码展示页面
   - 修改注册流程，支持邀请码
   - 开发邀请统计和排行榜页面
   - 实现邀请活动展示功能

4. **测试与优化**：
   - 功能测试：确保邀请流程正常
   - 性能测试：高并发下的系统稳定性
   - 安全测试：防刷机制有效性

## 八、后续优化方向

1. **邀请码有效期**：
   - 设置邀请码的有效期，过期后需要重新生成
   - 提供邀请码有效期提醒功能
   - 支持手动刷新邀请码

2. **邀请关系展示**：
   - 在用户个人中心展示已邀请用户列表
   - 提供邀请关系可视化展示
   - 支持按时间、状态筛选已邀请用户

3. **邀请排行榜**：
   - 增加邀请排行榜功能，激励用户积极邀请
   - 为排行榜前列用户提供额外奖励
   - 支持不同时间维度的排行榜

4. **活动支持**：
   - 支持特定时期提高邀请奖励，促进用户增长
   - 开发活动管理后台，灵活配置活动规则
   - 提供活动效果分析工具

## 九、UML图示

### 类图

```mermaid
classDiagram
    class User {
        +_id: String
        +openid: String
        +nickName: String
        +avatarUrl: String
        +points: Integer
    }

    class InvitationCode {
        +_id: String
        +user_id: String
        +code: String
        +create_time: DateTime
        +expire_time: DateTime
        +status: String
        +usage_count: Integer
    }

    class InvitationRecord {
        +_id: String
        +inviter_id: String
        +invitee_id: String
        +code: String
        +create_time: DateTime
        +status: String
        +points_awarded: Integer
        +activity_id: String
    }

    class InvitationActivity {
        +_id: String
        +name: String
        +description: String
        +start_time: DateTime
        +end_time: DateTime
        +points_multiplier: Float
        +status: String
    }

    class PointLog {
        +_id: String
        +action: String
        +create_time: DateTime
        +points: Integer
        +user_id: String
        +description: String
    }

    User "1" -- "1" InvitationCode : has >
    User "1" -- "0..*" InvitationRecord : invites >
    InvitationRecord "0..*" -- "0..1" InvitationActivity : belongs to >
    User "1" -- "0..*" PointLog : earns >
```

### 流程图

```mermaid
flowchart TD
    A[用户A获取邀请码] --> B[用户A分享邀请码或链接]
    B --> C[用户B接收邀请]
    C --> D{验证邀请码}
    D -- 无效或过期 --> E[提示邀请码无效]
    D -- 有效 --> F[用户B注册账号]
    F --> G[记录邀请关系]
    G --> H[检查每日邀请限制]
    H -- 未达上限 --> I[发放积分奖励]
    H -- 已达上限 --> J[记录关系但不奖励]
    I --> K[更新邀请统计]
    J --> K
    K --> L[更新排行榜数据]
```

## 十、注意事项

1. **与现有系统集成**：确保与现有积分系统无缝集成，使用统一的积分操作接口
2. **用户体验**：简化邀请流程，提高用户参与度
3. **数据监控**：定期检查邀请数据，防止刷邀请行为
4. **合规性**：确保邀请活动符合微信小程序相关规定
5. **文档更新**：及时更新积分规则文档，确保用户了解最新奖励机制
6. **性能考虑**：邀请排行榜等统计数据应考虑性能优化，避免频繁计算 

## 十一、技术实现细节

### 1. 每日邀请限制实现

为了确保每个用户每天最多邀请10人获得奖励的限制，系统将采用以下技术实现方案：

#### 1.1 数据库计数方法

在处理邀请完成时，系统会查询当天该用户已成功邀请且获得奖励的记录数量。具体实现步骤如下：

- 获取当前日期的开始时间（0点0分0秒）和结束时间（23点59分59秒）
- 查询`invitation_records`表中符合条件的记录数：
  - 邀请人ID为当前用户
  - 状态为已完成
  - 已奖励积分大于0
  - 创建时间在当天范围内
- 如果记录数量已达到10条，则不再奖励积分，但仍记录邀请关系
- 将邀请记录的`points_awarded`字段设为0，并添加备注"已达每日邀请奖励上限"

#### 1.2 性能优化方案

为提高系统性能，特别是在高并发场景下，可以采用以下优化方案：

- **计数器缓存**：为每个用户每天创建一个计数器记录，记录当日邀请获得奖励的次数
- **计数器结构**：
  - 键名格式：`invite_count:{用户ID}:{日期}`（例如：`invite_count:user123:2023-11-15`）
  - 值：当日已获得奖励的邀请次数
  - 过期时间：设置为24小时后自动过期，减少数据库存储压力
- **原子操作**：使用数据库的原子操作进行计数器更新，避免并发问题
- **前端提示**：在用户界面显示当日剩余可获得奖励的邀请次数，提高用户体验

#### 1.3 前端实现

在邀请页面中，系统将实时显示用户当日邀请情况：

- 加载页面时获取用户当日已邀请且获得奖励的次数
- 计算并显示剩余可获得奖励的邀请次数（10减去已获得奖励的次数）
- 当达到每日上限时，显示提示信息，但仍允许用户分享邀请码
- 提供邀请历史记录，区分已获得奖励和未获得奖励（已达上限）的邀请

### 2. 重复邀请检测实现

为确保每个用户只能被邀请一次，系统将采用多层次的检测机制：

#### 2.1 邀请记录唯一性检查

在验证邀请码时，系统会检查被邀请用户是否已存在邀请记录：

- 根据被邀请用户ID查询`invitation_records`表
- 检查是否存在状态为已完成的记录
- 如果存在，则拒绝本次邀请，返回"该用户已被邀请过，不能重复被邀请"的提示
- 同时检查邀请码有效性、过期状态等其他条件

#### 2.2 注册流程集成

在用户注册流程中集成邀请码验证：

- 注册页面支持从URL参数自动填写邀请码
- 在提交注册信息前，先验证邀请码有效性
- 验证时传入当前设备标识或临时用户ID，用于重复检测
- 验证成功后，在界面上显示邀请人信息（可选功能）
- 注册成功后，立即调用完成邀请的接口，记录邀请关系并发放奖励

#### 2.3 数据库约束

通过数据库层面的约束确保数据一致性：

- 在`invitation_records`表的`invitee_id`字段上创建唯一索引
- 使用条件唯一索引，仅对状态为"completed"的记录生效
- 这样即使有并发请求，数据库也会确保每个用户只能被成功邀请一次

#### 2.4 事务处理

使用数据库事务确保邀请完成过程的原子性：

- 在完成邀请处理中，使用事务包装所有数据库操作
- 事务内再次检查用户是否已被邀请，确保最终一致性
- 检查每日邀请限制
- 添加邀请记录
- 更新用户积分（如果有奖励）
- 添加积分记录
- 任何步骤失败，则回滚整个事务，确保数据一致性

### 3. 综合防刷机制

结合上述两个机制，系统形成了完善的防刷保护：

- **时间维度限制**：每个用户每天最多获得10次邀请奖励
- **用户唯一性限制**：每个用户只能被邀请一次
- **异常行为检测**：监控短时间内大量成功邀请的行为
- **数据库约束**：通过唯一索引和事务确保数据一致性

通过这些技术实现，邀请码系统能够有效防止刷邀请行为，保证系统公平性和数据准确性，同时提供良好的用户体验。 