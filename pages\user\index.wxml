<!-- 用户中心 -->
<view class="container">
  <view class="user-header">
    <view class="user-info">
      <view class="nickname">{{userInfo.nickname || '未命名用户'}}</view>
      <view class="role">{{roleText[userInfo.role] || '普通用户'}}</view>
      <view class="user-id-container">
        <text class="user-id">ID: {{userInfo._id || '未知'}}</text>
        <view class="copy-btn" bindtap="copyUserId">
          <view class="copy-icon"></view>
        </view>
      </view>
    </view>
  </view>

  <view class="stat-box">
    <view class="stat-item">
      <text class="num">{{userInfo.contribution.submitted || 0}}</text>
      <text class="label">已提交</text>
    </view>
    <view class="stat-item">
      <text class="num">{{userInfo.contribution.approved || 0}}</text>
      <text class="label">已通过</text>
    </view>
    <view class="stat-item">
      <text class="num">{{userInfo.points || 0}}</text>
      <text class="label">积分</text>
    </view>
  </view>

  <view class="menu-list">
    <view class="menu-item" bindtap="goToSubmissions">
      <text>我的提交</text>
      <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
    </view>
    <view class="menu-item" bindtap="goToCollections">
      <text>我的收藏</text>
      <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
    </view>
    <view class="menu-item" bindtap="goToMyPoints">
      <text>我的积分</text>
      <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
    </view>
    <view class="menu-item" bindtap="goToInvitation">
      <text>邀请好友</text>
      <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
    </view>

    <!-- 新增：用户管理入口，仅管理员和审核员可见 -->
    <block wx:if="{{userInfo.role === 'admin' || userInfo.role === 'reviewer'}}">
      <view class="menu-item" bindtap="goToUserManagementPage">
        <text>用户积分管理</text>
        <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </block>

    <!-- 管理员菜单 -->
    <block wx:if="{{userInfo.role === 'admin'}}">
      <view class="menu-title">管理功能</view>
      <view class="menu-item" bindtap="goToAdminPanel">
        <text>管理后台</text>
        <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="goToUserManage">
        <text>用户管理</text>
        <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="goToSubmissionReview">
        <text>停车场审核</text>
        <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </block>

    <!-- 审核员菜单 -->
    <block wx:if="{{userInfo.role === 'reviewer'}}">
      <view class="menu-title">审核功能</view>
      <view class="menu-item" bindtap="goToReviewerPanel">
        <text>审核后台</text>
        <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
      </view>
      <view class="menu-item" bindtap="goToSubmissionReview">
        <text>停车场审核</text>
        <image class="arrow" src="/static/images/arrow-right.png" mode="aspectFit"></image>
      </view>
    </block>
  </view>

  <view class="logout" bindtap="handleLogout">退出登录</view>
</view>