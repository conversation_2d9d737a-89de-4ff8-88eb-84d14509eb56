// 邀请码展示页面
Page({
  data: {
    inviteCode: '', // 用户的邀请码
    expireTime: '', // 邀请码过期时间
    expireDays: 0, // 剩余有效天数
    inviteCount: 0, // 已邀请人数
    totalPointsEarned: 0, // 通过邀请获得的总积分
    invitedUsers: [], // 已邀请用户列表
    dailyInviteCount: 0, // 今日已邀请人数
    dailyRemaining: 10, // 今日剩余可获得奖励的邀请次数
    activeActivities: [], // 当前活跃的邀请活动
    loading: true, // 加载状态
    shareDialogVisible: false, // 分享弹窗可见性
    shareImage: '', // 分享图片
    shareLink: '', // 分享链接
  },

  onLoad() {
    // 初始化云环境
    if (!wx.cloud) {
      wx.showToast({
        title: '请使用2.2.3或以上基础库',
        icon: 'none'
      });
      return;
    }
    wx.cloud.init({
      env: wx.cloud.DYNAMIC_CURRENT_ENV,
      traceUser: true
    });
    
    this.loadInviteData();
    this.loadActiveActivities();
  },

  onShow() {
    // 每次页面显示时重新加载数据
    this.loadInviteData();
  },

  onPullDownRefresh() {
    // 下拉刷新
    Promise.all([
      this.loadInviteData(),
      this.loadActiveActivities()
    ]).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载邀请数据
  async loadInviteData() {
    this.setData({ loading: true });
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo._id) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/user/login'
          });
        }, 1500);
        return;
      }

      const res = await wx.cloud.callFunction({
        name: 'getUserInviteStats',
        data: { userId: userInfo._id }
      });

      console.log('获取邀请数据结果：', res);

      if (res.result && res.result.success) {
        const data = res.result.data;
        
        // 计算剩余有效天数
        let expireDays = 0;
        if (data.expireTime) {
          const expireDate = new Date(data.expireTime);
          const today = new Date();
          expireDays = Math.ceil((expireDate - today) / (1000 * 60 * 60 * 24));
        }

        // 计算今日剩余可获得奖励的邀请次数
        const dailyRemaining = Math.max(0, 10 - (data.dailyInviteCount || 0));

        this.setData({
          inviteCode: data.code || '',
          expireTime: data.expireTime ? this.formatDate(new Date(data.expireTime)) : '',
          expireDays: expireDays,
          inviteCount: data.inviteCount || 0,
          totalPointsEarned: data.totalPointsEarned || 0,
          invitedUsers: data.invitedUsers || [],
          dailyInviteCount: data.dailyInviteCount || 0,
          dailyRemaining: dailyRemaining,
          loading: false
        });

        // 如果没有邀请码，自动生成
        if (!data.code) {
          this.generateInviteCode();
        }
      } else {
        wx.showToast({
          title: '获取邀请数据失败',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    } catch (e) {
      console.error('加载邀请数据异常：', e);
      wx.showToast({
        title: '加载数据失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 加载活跃的邀请活动
  async loadActiveActivities() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'getActiveInviteActivities'
      });

      console.log('获取活跃活动结果：', res);

      if (res.result && res.result.success) {
        this.setData({
          activeActivities: res.result.activities || []
        });
      }
    } catch (e) {
      console.error('加载活动数据异常：', e);
    }
  },

  // 生成邀请码
  async generateInviteCode() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo._id) {
        return;
      }

      wx.showLoading({ title: '生成邀请码中...' });

      const res = await wx.cloud.callFunction({
        name: 'generateInviteCode',
        data: { userId: userInfo._id }
      });

      console.log('生成邀请码结果：', res);

      if (res.result && res.result.success) {
        const { code, expireTime } = res.result;
        
        // 计算剩余有效天数
        const expireDate = new Date(expireTime);
        const today = new Date();
        const expireDays = Math.ceil((expireDate - today) / (1000 * 60 * 60 * 24));

        this.setData({
          inviteCode: code,
          expireTime: this.formatDate(expireDate),
          expireDays: expireDays
        });

        wx.showToast({
          title: '邀请码生成成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.result.message || '生成邀请码失败',
          icon: 'none'
        });
      }
    } catch (e) {
      console.error('生成邀请码异常：', e);
      wx.showToast({
        title: '生成邀请码失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 复制邀请码
  copyInviteCode() {
    if (!this.data.inviteCode) {
      wx.showToast({
        title: '邀请码不存在',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: this.data.inviteCode,
      success: () => {
        wx.showToast({
          title: '邀请码已复制',
          icon: 'success'
        });
      }
    });
  },

  // 显示分享弹窗
  showShareDialog() {
    // 生成分享链接
    const shareLink = `https://example.com/register?invite_code=${this.data.inviteCode}`;
    
    this.setData({
      shareDialogVisible: true,
      shareLink: shareLink
    });

    // 生成分享图片（小程序码）
    this.generateShareImage();
  },

  // 关闭分享弹窗
  closeShareDialog() {
    this.setData({
      shareDialogVisible: false
    });
  },

  // 生成分享图片
  async generateShareImage() {
    if (!this.data.inviteCode) return;

    try {
      const res = await wx.cloud.callFunction({
        name: 'generateInviteQrCode',
        data: { 
          inviteCode: this.data.inviteCode,
          page: 'pages/user/register'
        }
      });

      console.log('生成小程序码结果：', res);

      if (res.result && res.result.success) {
        this.setData({
          shareImage: res.result.fileID
        });
      }
    } catch (e) {
      console.error('生成分享图片异常：', e);
    }
  },

  // 保存分享图片到相册
  saveShareImage() {
    if (!this.data.shareImage) {
      wx.showToast({
        title: '分享图片不存在',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '保存中...' });

    wx.cloud.downloadFile({
      fileID: this.data.shareImage,
      success: res => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.hideLoading();
            wx.showToast({
              title: '已保存到相册',
              icon: 'success'
            });
          },
          fail: err => {
            wx.hideLoading();
            console.error('保存图片失败：', err);
            wx.showToast({
              title: '保存失败，请检查权限',
              icon: 'none'
            });
          }
        });
      },
      fail: err => {
        wx.hideLoading();
        console.error('下载图片失败：', err);
        wx.showToast({
          title: '图片下载失败',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到排行榜页面
  goToRanking() {
    wx.navigateTo({
      url: '/pages/invitation/ranking'
    });
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 分享给朋友
  onShareAppMessage() {
    const userInfo = wx.getStorageSync('userInfo');
    const nickname = userInfo?.nickname || userInfo?.username || '好友';
    
    return {
      title: `${nickname}邀请您加入捉车位，共享停车位信息`,
      path: `/pages/user/register?invite_code=${this.data.inviteCode}`,
      imageUrl: '/static/images/share_invite.png' // 分享图片路径，需要替换为实际图片
    };
  }
}) 