const app = getApp(); // 获取全局App实例，如果需要全局数据或方法的话

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentUserInfo: null, // 当前登录的用户信息 (包含角色)
    targetUserInfo: null, // 正在查看的积分所属用户信息 (昵称，头像)
    totalPoints: 0, // 用户总积分
    pointRecords: [], // 积分记录列表
    isLoading: true, // 是否正在加载数据
    hasMoreData: true, // 是否还有更多数据可加载
    currentPage: 1, // 当前页码
    pageSize: 15, // 每页加载的记录数
    errorOccurred: false, // 是否发生错误
    errorMessage: '', // 错误信息
    viewingUserId: null, // 最终确定要查看哪个用户的ID
    pageTitle: '我的积分', // 页面标题，可以根据是否查看他人积分动态改变
    showPointsOperationBtn: false, // 是否显示积分操作按钮
    showExplanation: false // 是否显示积分说明
  },

  /**
   * 生命周期函数--监听页面加载
   * options: 页面启动参数
   */
  onLoad: function (options) {
    console.log('积分页面 onLoad, options:', options);
    const storedCurrentUserInfo = wx.getStorageSync('userInfo'); // 当前登录用户信息

    if (!storedCurrentUserInfo || !storedCurrentUserInfo._id) {
      console.error('无法获取当前登录用户信息，请确保用户已登录。');
      wx.showToast({
        title: '请先登录',
        icon: 'error',
        duration: 2000,
        complete: () => wx.navigateBack() // 或跳转到登录页
      });
      this.setData({ isLoading: false, errorOccurred: true, errorMessage: '请先登录后再查看积分。' });
      return;
    }
    this.setData({ currentUserInfo: storedCurrentUserInfo });

    let viewingUserId = storedCurrentUserInfo._id; // 默认查看自己的积分
    let pageTitle = '我的积分';
    
    // 判断是否显示积分操作按钮（只有管理员和审核员可以操作积分）
    const showPointsOperationBtn = storedCurrentUserInfo.role === 'admin' || storedCurrentUserInfo.role === 'reviewer';
    this.setData({ showPointsOperationBtn });

    // options.targetUserId 是从其他页面（如用户管理列表）跳转过来时，指定要查看的用户ID
    if (options.targetUserId && options.targetUserId !== storedCurrentUserInfo._id) {
      // 只有管理员或审核员才有权限查看他人的积分
      if (storedCurrentUserInfo.role === 'admin' || storedCurrentUserInfo.role === 'reviewer') {
        viewingUserId = options.targetUserId;
        // pageTitle = '用户积分详情'; // 可以动态修改标题，但需要目标用户的昵称，在loadUserPointsDetails获取
        console.log(`管理员/审核员正在查看用户 ${viewingUserId} 的积分`);
      } else {
        console.warn('普通用户尝试查看他人积分，权限不足，将显示自己的积分。');
        // 非管理员/审核员，即使有targetUserId，也只显示自己的
      }
    }

    this.setData({
      viewingUserId: viewingUserId,
      isLoading: true,
      errorOccurred: false,
      errorMessage: '',
      pageTitle: pageTitle // 初始标题
    });

    if (viewingUserId) {
      this.loadUserPointsDetails();
    } else {
        // 理论上此分支不会进入，因为前面有登录校验
        this.setData({isLoading: false, errorOccurred: true, errorMessage: '无法确定要查看的用户ID'});
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // console.log('积分页面 onShow');
    // 如果需要，可以在这里添加刷新逻辑，但要注意避免不必要的刷新
  },

  /**
   * 加载用户积分详情和记录 (核心函数)
   * @param {boolean} isLoadingMore 是否为加载更多操作，true表示加载更多，false表示首次加载或刷新
   */
  loadUserPointsDetails: async function (isLoadingMore = false) {
    if (!this.data.viewingUserId) {
      this.setData({ isLoading: false, errorOccurred: true, errorMessage: '目标用户ID丢失' });
      return;
    }
    if (!this.data.currentUserInfo || !this.data.currentUserInfo._id) {
        this.setData({ isLoading: false, errorOccurred: true, errorMessage: '无法获取当前用户信息，请重新登录。' });
        return;
    }

    if (!isLoadingMore) {
      this.setData({ isLoading: true, currentPage: 1, pointRecords: [], hasMoreData: true, errorOccurred: false });
    } else {
      if (!this.data.hasMoreData || this.data.isLoading) return;
      this.setData({ isLoading: true });
    }

    wx.showNavigationBarLoading();

    try {
      console.log(`调用云函数 getUserPointsDetails, 实际查询的用户ID (viewingUserId): ${this.data.viewingUserId}, page: ${this.data.currentPage}`);
      const res = await wx.cloud.callFunction({
        name: 'getUserPointsDetails',
        data: {
          callerId: this.data.currentUserInfo._id,  // 当前登录用户的ID
          targetUserId: this.data.viewingUserId,    // 要查看积分的用户ID
          page: this.data.currentPage,
          pageSize: this.data.pageSize
        }
      });

      console.log('getUserPointsDetails 云函数返回结果:', res);

      if (res.result && res.result.success) {
        const { userInfo, pointsRecords } = res.result.data;
        
        // 格式化积分记录的时间
        const formattedRecords = pointsRecords.records.map(record => {
          if (record.create_time) {
            const date = new Date(record.create_time);
            record.create_time_formatted = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
          }
          return record;
        });
        
        let newPageTitle = this.data.pageTitle;
        // 如果是查看他人积分且成功获取到昵称，更新标题
        if (this.data.viewingUserId !== this.data.currentUserInfo._id && userInfo.nickname) {
            newPageTitle = `${userInfo.nickname}的积分`;
        }
        wx.setNavigationBarTitle({ title: newPageTitle });

        this.setData({
          targetUserInfo: { // 这是被查看积分的那个用户的信息
             nickName: userInfo.nickname,
             avatarUrl: userInfo.avatarUrl,
             points: userInfo.points
          },
          totalPoints: userInfo.points,
          pointRecords: isLoadingMore ? this.data.pointRecords.concat(formattedRecords) : formattedRecords,
          hasMoreData: pointsRecords.currentPage < pointsRecords.totalPages,
          currentPage: pointsRecords.currentPage,
          isLoading: false,
          pageTitle: newPageTitle
        });
      } else {
        throw new Error(res.result.error || '获取积分数据失败');
      }
    } catch (error) {
      console.error('调用getUserPointsDetails云函数失败:', error);
      const errMsg = error.message || '加载数据失败，请稍后再试';
      this.setData({ isLoading: false, errorOccurred: true, errorMessage: errMsg });
      wx.showToast({ title: errMsg, icon: 'none' });
    }
    wx.hideNavigationBarLoading();
    if (!isLoadingMore) wx.stopPullDownRefresh();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作 (下拉刷新)
   */
  onPullDownRefresh: function () {
    if (this.data.isLoading) return;
    this.resetAndReloadData();
  },

  /**
   * 重置数据并重新加载
   */
  resetAndReloadData: function() {
    this.setData({ currentPage: 1, pointRecords: [], hasMoreData: true, errorOccurred: false }, () => {
      this.loadUserPointsDetails(false);
    });
  },

  /**
   * 页面上拉触底事件的处理函数 (加载更多)
   */
  onReachBottom: function () {
    if (this.data.hasMoreData && !this.data.isLoading) {
      this.setData({ currentPage: this.data.currentPage + 1 }, () => {
        this.loadUserPointsDetails(true);
      });
    } else if (!this.data.hasMoreData) {
      // wx.showToast({ title: '没有更多了', icon: 'none' });
    }
  },

  /**
   * 切换积分说明的显示状态
   */
  togglePointsExplanation: function() {
    this.setData({
      showExplanation: !this.data.showExplanation
    });
  },

  /**
   * 跳转到积分操作页面
   * 管理员和审核员可以操作用户积分
   */
  navigateToPointsOperation: function() {
    if (!this.data.viewingUserId) {
      wx.showToast({
        title: '无法获取用户信息',
        icon: 'error'
      });
      return;
    }
    
    // 跳转到新的积分操作页面
    wx.navigateTo({
      url: `/pages/points/points_operation/index?userId=${this.data.viewingUserId}`,
      success: () => {
        console.log('成功跳转到积分操作页面');
      },
      fail: (error) => {
        console.error('跳转到积分操作页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    // 分享时，如果正在查看的是他人积分，可以考虑分享该用户的积分页（如果业务允许）
    // 否则默认分享自己的
    const shareUserId = this.data.viewingUserId || this.data.currentUserInfo._id;
    let shareTitle = '我的积分详情';
    if (this.data.targetUserInfo && this.data.targetUserInfo.nickName && this.data.viewingUserId !== this.data.currentUserInfo._id) {
        shareTitle = `${this.data.targetUserInfo.nickName}的积分详情`;
    }

    return {
      title: shareTitle,
      path: `/pages/points/index?targetUserId=${shareUserId}`
    };
  }
}); 