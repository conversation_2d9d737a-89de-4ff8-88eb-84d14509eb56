感谢您提供 `users` 集合的格式。根据您提供的信息，我们可以对 `users` 表的设计进行调整，以确保它符合您的需求。以下是更新后的数据库设计和UML类图。

### 二、数据库设计

#### 用户表（`users`）
存储用户的基本信息和积分信息。

| 字段名       | 类型         | 描述                     |
|--------------|--------------|--------------------------|
| _id          | String       | 用户ID（主键）           |
| openid       | String       | 微信openid               |
| nickName     | String       | 用户昵称                 |
| avatarUrl    | String       | 用户头像URL              |
| points       | Integer      | 用户积分                 |
| role         | String       | 用户角色（如admin, user）|
| status       | String       | 用户状态（如active）     |
| submit_count | Integer      | 提交停车场数量           |
| create_time  | DateTime     | 注册时间                 |
| last_login_time | DateTime  | 最后登录时间             |
| invite_code  | String       | 用户的唯一邀请码         |
| invited_by   | String       | 邀请该用户的用户ID       |
| invited_users | Array       | 该用户已成功邀请的用户ID列表 |
| invite_count | Integer      | 成功邀请的用户数量       |

#### 积分记录表（`point_logs`）
记录用户的积分变动情况。

| 字段名       | 类型         | 描述                     |
|--------------|--------------|--------------------------|
| _id          | String       | 记录ID（主键）           |
| action       | String       | 操作类型（view）         |
| create_time  | DateTime     | 创建时间                 |
| is_premium   | Boolean      | 是否优质停车场           |
| parking_id   | String       | 相关停车场ID             |
| points       | Integer      | 积分变化（正数增加，负数减少） |
| user_id      | String       | 用户ID                   |

#### 邀请记录表（`invitation_records`）
记录用户的邀请关系和奖励情况。

| 字段名       | 类型         | 描述                     |
|--------------|--------------|--------------------------|
| _id          | String       | 记录ID（主键）           |
| inviter_id   | String       | 邀请人ID                 |
| invitee_id   | String       | 被邀请人ID               |
| code         | String       | 使用的邀请码             |
| status       | String       | 状态（pending/completed）|
| create_time  | DateTime     | 邀请时间                 |
| complete_time | DateTime    | 完成时间                 |
| points_awarded | Integer    | 奖励的积分数量           |
| activity_id  | String       | 关联的活动ID（如果有）   |
| daily_count  | Integer      | 邀请人当日邀请计数       |

#### 邀请码表（`invitation_codes`）
存储用户的邀请码信息。

| 字段名       | 类型         | 描述                     |
|--------------|--------------|--------------------------|
| _id          | String       | 记录ID（主键）           |
| user_id      | String       | 用户ID                   |
| code         | String       | 邀请码                   |
| status       | String       | 状态（active/inactive）  |
| create_time  | DateTime     | 创建时间                 |
| expire_time  | DateTime     | 过期时间                 |
| usage_count  | Integer      | 使用次数                 |

#### 邀请活动表（`invitation_activities`）
记录邀请活动信息。

| 字段名       | 类型         | 描述                     |
|--------------|--------------|--------------------------|
| _id          | String       | 记录ID（主键）           |
| name         | String       | 活动名称                 |
| description  | String       | 活动描述                 |
| start_time   | DateTime     | 开始时间                 |
| end_time     | DateTime     | 结束时间                 |
| status       | String       | 状态（active/inactive）  |
| points_multiplier | Number  | 积分倍数                 |
| create_time  | DateTime     | 创建时间                 |

### 1. 积分获取方式

| 操作 | 获得积分 | 说明 | 每日限制 |
|------|----------|------|----------|
| 新用户注册 | +20分 | 一次性奖励 | - |
| 推荐新用户 | +10分 | 新用户通过分享链接注册 | 5次/天 |
| 提交普通停车场 | +10分 | 提交后即获得 | - |
| 提交优质停车场 | +20分 | 提交后即获得 | - |
| 审核通过奖励 | +1-2分 | 额外10%奖励 | - |
| 评论普通停车场 | +3分 | 需审核通过 | 10次/天 |
| 评论优质停车场 | +5分 | 需审核通过 | 10次/天 |
| 邀请新用户注册 | +10分 | 通过邀请码或分享链接 | 10次/天 |
| 每日积分上限 | - | - | 100分/天 |

### 2. 积分消耗方式

| 操作 | 消耗积分 | 说明 | 每日限制 |
|------|----------|------|----------|
| 查看普通停车场详情 | -15分 | 已查看过的不重复扣分 | - |
| 查看优质停车场详情 | -30分 | 已查看过的不重复扣分 | 5次/天 |

### 3. 邀请码系统积分规则

| 操作 | 积分变化 | 说明 | 限制 |
|------|----------|------|------|
| 邀请新用户注册 | +10分 | 被邀请用户完成注册后 | 每日最多10次 |
| 被邀请注册 | +5分 | 使用邀请码注册的新用户额外奖励 | 一次性 |
| 邀请用户排行榜奖励 | +50分 | 每月邀请排行榜前10名额外奖励 | 每月1次 |

### 四、UML图示

#### 类图

```mermaid
classDiagram
    class User {
        +_id: String
        +openid: String
        +nickName: String
        +avatarUrl: String
        +points: Integer
        +role: String
        +status: String
        +submit_count: Integer
        +create_time: DateTime
        +last_login_time: DateTime
        +invite_code: String
        +invited_by: String
        +invited_users: Array
        +invite_count: Integer
    }

    class PointLog {
        +_id: String
        +action: String
        +create_time: DateTime
        +is_premium: Boolean
        +parking_id: String
        +points: Integer
        +user_id: String
    }
    
    class InvitationRecord {
        +_id: String
        +inviter_id: String
        +invitee_id: String
        +code: String
        +status: String
        +create_time: DateTime
        +complete_time: DateTime
        +points_awarded: Integer
        +activity_id: String
        +daily_count: Integer
    }
    
    class InvitationCode {
        +_id: String
        +user_id: String
        +code: String
        +status: String
        +create_time: DateTime
        +expire_time: DateTime
        +usage_count: Integer
    }
    
    class InvitationActivity {
        +_id: String
        +name: String
        +description: String
        +start_time: DateTime
        +end_time: DateTime
        +status: String
        +points_multiplier: Number
        +create_time: DateTime
    }

    User "1" -- "0..*" PointLog : has >
    User "1" -- "0..*" InvitationRecord : invites >
    User "1" -- "0..1" InvitationCode : has >
    InvitationRecord "0..*" -- "0..1" InvitationActivity : relates to >
```

#### 用例图

```mermaid
usecaseDiagram
    actor User as 用户
    actor Admin as 管理员

    用户 --> (增加积分)
    用户 --> (减少积分)
    用户 --> (查询积分)
    用户 --> (邀请新用户)
    用户 --> (查看邀请记录)
    管理员 --> (手动增加积分)
    管理员 --> (手动减少积分)
    管理员 --> (查询积分记录)
    管理员 --> (查看邀请排行)
```

### 五、注意事项

1. **积分安全**：所有积分操作都在云函数中进行，防止客户端篡改。
2. **防刷机制**：设置每日获取积分上限，防止恶意刷分。
3. **数据一致性**：使用数据库事务确保积分变化的原子性。
4. **用户体验**：积分不足时给出明确提示和获取积分的方法。
5. **邀请码安全**：邀请码生成加入随机因素，避免被猜测或批量生成。
6. **防止刷邀请**：同一设备/IP短时间内注册的多个账号视为无效邀请。
7. **表名规范**：严格遵循数据库表命名规范，确保代码中的表名与实际数据库一致。

### 六、数据库表命名规范

为避免表名不一致导致的问题，特制定以下命名规范：

1. **表名命名**：
   * 使用小写字母和下划线
   * 多个单词用下划线连接
   * 表名应明确表示表的内容和用途
   * 避免使用复数形式（如使用`point_log`而不是`point_logs`）

2. **关键表名**：
   * `users`: 用户表
   * `point_logs`: 积分记录表
   * `invitation_codes`: 邀请码表
   * `invitation_records`: 邀请记录表
   * `invitation_activities`: 邀请活动表

3. **代码中的表名引用**：
   * 使用常量定义表名，避免硬编码
   * 在代码中严格使用与数据库一致的表名
   * 修改表名时同步更新所有相关代码

### 七、后续优化建议

1. **增加积分兑换功能**：如兑换停车优惠券。
2. **增加积分排行榜**：激励用户参与。
3. **节假日双倍积分活动**。
4. **VIP会员制度**：减少积分消耗。
5. **积分有效期机制**：防止积分囤积。
6. **多级邀请奖励**：实现邀请链，奖励邀请人的邀请人。
7. **邀请活动**：特定时期提高邀请奖励，促进用户增长。
8. **命名规范工具**：开发工具自动检查代码中的表名与数据库是否一致。

通过以上设计和实现，你可以构建一个功能完善、安全可靠的积分系统，满足微信小程序的基本积分管理需求。后续可以根据业务需求逐步扩展更多功能。