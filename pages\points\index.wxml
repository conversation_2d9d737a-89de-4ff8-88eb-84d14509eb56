<!--pages/points/index.wxml-->
<view class="container points-page">
  <!-- 用户信息和总积分展示区域 -->
  <view class="user-profile-section" wx:if="{{!isLoading || pointRecords.length > 0}}">
    <view class="user-info-box">
      <image class="avatar" src="{{targetUserInfo.avatarUrl || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="nickname">{{targetUserInfo.nickName || (viewingUserId === currentUserInfo._id ? currentUserInfo.nickName : '访客')}}</view>
    </view>
    <view class="total-points-box">
      <text class="points-label">当前积分</text>
      <text class="points-value">{{totalPoints}}</text>
      <!-- 积分操作按钮 - 仅管理员和审核员可见 -->
      <button wx:if="{{showPointsOperationBtn}}" class="points-operation-btn" bindtap="navigateToPointsOperation">积分操作</button>
    </view>

    <!-- 积分说明区域 -->
    <view class="points-explanation-section">
      <view class="explanation-toggle" bindtap="togglePointsExplanation">
        <text class="explanation-title">积分说明</text>
        <text class="toggle-icon {{showExplanation ? 'expanded' : ''}}">▼</text>
      </view>

      <view class="explanation-content {{showExplanation ? 'show' : 'hide'}}">
        <!-- 积分获取方式 -->
        <view class="explanation-category">
          <view class="category-title">
            <text class="category-icon">💰</text>
            <text class="category-text">积分获取</text>
          </view>
          <view class="rule-list">
            <view class="rule-item">
              <text class="rule-action">新用户注册</text>
              <text class="rule-points gain">+20分</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">推荐新用户</text>
              <text class="rule-points gain">+10分</text>
              <text class="rule-limit">(5次/天)</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">提交普通停车场</text>
              <text class="rule-points gain">+10分</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">提交优质停车场</text>
              <text class="rule-points gain">+20分</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">评论普通停车场</text>
              <text class="rule-points gain">+3分</text>
              <text class="rule-limit">(10次/天)</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">评论优质停车场</text>
              <text class="rule-points gain">+5分</text>
              <text class="rule-limit">(10次/天)</text>
            </view>
          </view>
        </view>

        <!-- 积分消耗方式 -->
        <view class="explanation-category">
          <view class="category-title">
            <text class="category-icon">💸</text>
            <text class="category-text">积分消耗</text>
          </view>
          <view class="rule-list">
            <view class="rule-item">
              <text class="rule-action">查看普通停车场详情</text>
              <text class="rule-points cost">-15分</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">查看优质停车场详情</text>
              <text class="rule-points cost">-30分</text>
              <text class="rule-limit">(5次/天)</text>
            </view>
          </view>
        </view>

        <!-- 邀请系统 -->
        <view class="explanation-category">
          <view class="category-title">
            <text class="category-icon">🎁</text>
            <text class="category-text">邀请奖励</text>
          </view>
          <view class="rule-list">
            <view class="rule-item">
              <text class="rule-action">邀请新用户注册</text>
              <text class="rule-points gain">+10分</text>
              <text class="rule-limit">(10次/天)</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">被邀请注册奖励</text>
              <text class="rule-points gain">+5分</text>
              <text class="rule-limit">(一次性)</text>
            </view>
            <view class="rule-item">
              <text class="rule-action">月度邀请排行榜</text>
              <text class="rule-points gain">+50分</text>
              <text class="rule-limit">(前10名)</text>
            </view>
          </view>
        </view>

        <!-- 重要提示 -->
        <view class="explanation-tips">
          <text class="tips-title">💡 重要提示</text>
          <text class="tips-content">• 每日积分获取上限：100分</text>
          <text class="tips-content">• 已查看过的停车场不重复扣分</text>
          <text class="tips-content">• 审核通过后可获得额外10%奖励</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态提示 -->
  <view class="loading-indicator" wx:if="{{isLoading && currentPage === 1 && !errorOccurred}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <!-- 错误提示 -->
  <view class="error-message-section" wx:if="{{errorOccurred}}">
    <text>{{errorMessage || '加载数据失败，请稍后重试。'}}</text>
    <button class="retry-button" bindtap="resetAndReloadData">点击重试</button>
  </view>

  <!-- 积分记录列表 -->
  <view class="points-records-section" wx:if="{{!isLoading || pointRecords.length > 0}}">
    <view class="records-header">积分明细</view>
    <scroll-view scroll-y class="records-scroll-view" wx:if="{{pointRecords.length > 0}}">
      <block wx:for="{{pointRecords}}" wx:key="_id">
        <view class="record-item {{item.points > 0 ? 'income' : 'expense'}}">
          <view class="record-left">
            <view class="record-description">{{item.description}}</view>
            <view class="record-action-type">类型: {{item.action}}</view> <!-- 根据实际需要显示action -->
            <view class="record-time">{{item.create_time_formatted}}</view> <!-- 假设JS中已格式化时间 -->
          </view>
          <view class="record-right">
            <text class="points-change {{item.points > 0 ? 'points-positive' : 'points-negative'}}">
              {{item.points > 0 ? '+' : ''}}{{item.points}}
            </text>
          </view>
        </view>
      </block>
      <!-- 加载更多提示 -->
      <view class="loading-more-indicator" wx:if="{{isLoading && currentPage > 1}}">
        <text>正在加载更多...</text>
      </view>
      <view class="no-more-data-indicator" wx:if="{{!hasMoreData && pointRecords.length > 0 && !isLoading}}">
        <text>--- 没有更多记录了 ---</text>
      </view>
    </scroll-view>

    <!-- 空状态：无积分记录 -->
    <view class="empty-state" wx:if="{{pointRecords.length === 0 && !isLoading && !errorOccurred}}">
      <text>暂无积分记录</text>
    </view>
  </view>
</view> 