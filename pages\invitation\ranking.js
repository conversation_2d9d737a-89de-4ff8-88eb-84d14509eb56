// 邀请排行榜页面
Page({
  data: {
    currentTab: 'total', // 当前选中的榜单类型：日榜(daily)、周榜(weekly)、月榜(monthly)、总榜(total)
    rankings: [], // 排行榜数据
    userRank: null, // 当前用户的排名信息
    loading: true, // 加载状态
  },

  onLoad() {
    // 初始化云环境
    if (!wx.cloud) {
      wx.showToast({
        title: '请使用2.2.3或以上基础库',
        icon: 'none'
      });
      return;
    }
    wx.cloud.init({
      env: wx.cloud.DYNAMIC_CURRENT_ENV,
      traceUser: true
    });
    
    this.loadRankingData(this.data.currentTab);
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.loadRankingData(this.data.currentTab).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 切换榜单类型
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.currentTab) {
      this.setData({ currentTab: tab, loading: true });
      this.loadRankingData(tab);
    }
  },

  // 加载排行榜数据
  async loadRankingData(type) {
    this.setData({ loading: true });
    try {
      const res = await wx.cloud.callFunction({
        name: 'getInviteRanking',
        data: { 
          type: type,
          limit: 50 // 最多显示50条排名
        }
      });

      console.log('获取排行榜数据结果：', res);

      if (res.result && res.result.success) {
        this.setData({
          rankings: res.result.rankings || [],
          userRank: res.result.userRank || null,
          loading: false
        });
      } else {
        wx.showToast({
          title: '获取排行榜失败',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    } catch (e) {
      console.error('加载排行榜数据异常：', e);
      wx.showToast({
        title: '加载数据失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 格式化排名
  formatRank(rank) {
    if (rank === 1) {
      return '🥇';
    } else if (rank === 2) {
      return '🥈';
    } else if (rank === 3) {
      return '🥉';
    } else {
      return rank;
    }
  },

  // 返回邀请页
  goBack() {
    wx.navigateBack();
  }
}) 