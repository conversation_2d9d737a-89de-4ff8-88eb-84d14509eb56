const app = getApp();

Page({
  data: {
    userList: [],
    isLoading: false,
    hasMoreData: true,
    currentPage: 1,
    pageSize: 20, // 与云函数中的pageSize一致
    errorOccurred: false,
    errorMessage: '',
    searchKeyword: '', // 搜索关键词
    roleFilter: '',    // 角色筛选
    // 角色显示映射表，可以根据您的实际情况调整
    roleMap: {
      'user': '普通用户',
      'normal': '普通用户',
      'vip': 'VIP用户',
      'admin': '管理员',
      'reviewer': '审核员'
    }
  },

  onLoad: function (options) {
    this.loadUserList(false); // 首次加载
  },

  onShow: function() {
    // 可选：如果希望每次进入页面都刷新，可以在这里调用 this.loadUserList(false);
    // 但要注意用户体验和数据刷新频率
  },

  /**
   * 加载用户列表
   * @param {boolean} isLoadingMore - 是否为加载更多操作
   */
  loadUserList: async function (isLoadingMore = false) {
    if (!isLoadingMore) {
      this.setData({ isLoading: true, currentPage: 1, userList: [], hasMoreData: true, errorOccurred: false });
    } else {
      if (!this.data.hasMoreData || this.data.isLoading) return;
      this.setData({ isLoading: true });
    }

    wx.showNavigationBarLoading();

    // 获取当前登录管理员的_id
    const currentUserInfo = wx.getStorageSync('userInfo');
    if (!currentUserInfo || !currentUserInfo._id) {
      this.setData({
        isLoading: false,
        errorOccurred: true,
        errorMessage: '无法获取管理员信息，请重新登录后再试。'
      });
      wx.hideNavigationBarLoading();
      wx.showModal({
          title: '错误',
          content: '无法获取您的管理员身份信息，请尝试重新登录。',
          showCancel: false,
          confirmText: '知道了'
      });
      return;
    }
    const callerId = currentUserInfo._id;

    try {
      const params = {
        callerId: callerId,
        page: this.data.currentPage,
        pageSize: this.data.pageSize,
        searchKeyword: this.data.searchKeyword || null,
        roleFilter: this.data.roleFilter || null
      };
      console.log('[用户管理] 调用adminGetUserList云函数，参数:', params);

      const res = await wx.cloud.callFunction({
        name: 'adminGetUserList',
        data: params
      });

      console.log('[用户管理] adminGetUserList返回结果:', res);

      if (res.result && res.result.success) {
        const data = res.result.data;
        const newUsers = (data.userList || []).map(user => {
          if (user.create_time) {
            const date = new Date(user.create_time);
            user.create_time_formatted = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
          }
          return user;
        });

        this.setData({
          userList: isLoadingMore ? this.data.userList.concat(newUsers) : newUsers,
          hasMoreData: data.currentPage < data.totalPages,
          currentPage: data.currentPage,
          isLoading: false
        });
      } else {
        throw new Error(res.result.error || '获取用户列表失败');
      }
    } catch (error) {
      console.error('[用户管理] 调用adminGetUserList云函数失败:', error);
      const errMsg = error.message || '加载用户数据失败，请稍后再试';
      if (error.message && error.message.includes('权限不足')) {
          // 如果是权限问题，引导用户检查登录或角色
          // 可以考虑跳转到登录页或提示联系管理员
          wx.showModal({
              title: '权限不足',
              content: '您可能没有权限访问此功能，请确保以管理员或审核员身份登录。',
              showCancel: false,
              confirmText: '知道了'
          });
      }
      this.setData({ isLoading: false, errorOccurred: true, errorMessage: errMsg });
      // wx.showToast({ title: errMsg, icon: 'none' }); // 错误信息已在页面展示
    }
    wx.hideNavigationBarLoading();
    if (!isLoadingMore) wx.stopPullDownRefresh();
  },

  onPullDownRefresh: function () {
    if (this.data.isLoading) return;
    this.loadUserList(false); // 下拉刷新，加载第一页
  },

  onReachBottom: function () {
    if (this.data.hasMoreData && !this.data.isLoading) {
      this.setData({ currentPage: this.data.currentPage + 1 }, () => {
        this.loadUserList(true); // 上拉加载更多
      });
    }
  },

  /**
   * 跳转到用户积分操作页面
   */
  navigateToUserPoints: function (event) {
    const userId = event.currentTarget.dataset.userid;
    const userNickName = event.currentTarget.dataset.nickname || '用户'; 

    if (!userId) {
      wx.showToast({ title: '无法获取用户ID', icon: 'none' });
      return;
    }

    // 更新日志信息，反映新的目标页面
    console.log(`[用户管理/navigateToUserPoints] 导航到用户 ${userNickName} (ID: ${userId}) 的积分操作页面`);
    
    // 更新跳转的 URL
    wx.navigateTo({
      url: `/pages/points/points_operation/index?userId=${userId}`,
      fail: (err) => {
        console.error('[用户管理/navigateToUserPoints] 跳转到积分操作页面失败:', err);
        wx.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  },

  // TODO: 实现搜索和筛选相关逻辑
  // onSearchConfirm: function(event) {
  //   const keyword = event.detail.value;
  //   this.setData({ searchKeyword: keyword, currentPage: 1 });
  //   this.loadUserList(false);
  // },
  // onChangeRoleFilter: function(event) { ... }
}); 