Page({
  data: {
    isAgree: false,
    username: '',
    nickname: '',
    password: '',
    confirmPassword: '',
    phone: '',
    inviteCode: ''
  },

  onLoad(options) {
    console.log('注册页面加载', options)
    // 初始化云环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
      return
    }
    wx.cloud.init({
      env: wx.cloud.DYNAMIC_CURRENT_ENV,
      traceUser: true
    })

    // 如果URL中包含邀请码参数，自动填写
    if (options && options.invite_code) {
      console.log('检测到邀请码参数：', options.invite_code)
      this.setData({
        inviteCode: options.invite_code
      })
    }
  },

  // 验证用户名格式
  validateUsername(username) {
    console.log('验证用户名格式', username)
    const usernameRegex = /^[a-zA-Z0-9_]{4,20}$/
    const result = usernameRegex.test(username)
    console.log('用户名验证结果:', result)
    return result
  },

  // 验证密码强度
  validatePassword(password) {
    console.log('验证密码强度')
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$/
    const result = passwordRegex.test(password)
    console.log('密码验证结果:', result)
    return result
  },

  // 验证手机号格式
  validatePhone(phone) {
    console.log('验证手机号格式', phone)
    const phoneRegex = /^1[3-9]\d{9}$/
    const result = phoneRegex.test(phone)
    console.log('手机号验证结果:', result)
    return result
  },

  // 验证邀请码
  async validateInviteCode(inviteCode) {
    if (!inviteCode) return true // 邀请码为空，视为有效（选填）
    
    console.log('验证邀请码', inviteCode)
    try {
      const res = await wx.cloud.callFunction({
        name: 'validateInviteCode',
        data: { code: inviteCode }
      })
      
      console.log('邀请码验证结果：', res)
      
      if (res.result && res.result.valid) {
        return true
      } else {
        this.showError(res.result.message || '邀请码无效')
        return false
      }
    } catch (e) {
      console.error('验证邀请码异常：', e)
      this.showError('邀请码验证失败，请重试')
      return false
    }
  },

  // 处理注册
  async handleRegister() {
    console.log('点击注册按钮')
    console.log('表单数据:', {
      username: this.data.username,
      nickname: this.data.nickname,
      phone: this.data.phone,
      isAgree: this.data.isAgree,
      inviteCode: this.data.inviteCode
    })
    
    if (!this.data.isAgree) {
      console.log('用户未同意协议')
      this.showError('请先同意用户协议和隐私政策')
      return
    }

    const { username, password, confirmPassword, nickname, phone, inviteCode } = this.data

    // 验证输入
    if (!username || !password || !confirmPassword || !phone) {
      console.log('注册信息不完整', { 
        hasUsername: !!username, 
        hasPassword: !!password, 
        hasConfirmPassword: !!confirmPassword, 
        hasPhone: !!phone 
      })
      this.showError('请填写完整注册信息')
      return
    }

    // 验证用户名格式
    if (!this.validateUsername(username)) {
      this.showError('用户名格式不正确（4-20位字母、数字或下划线）')
      return
    }

    // 验证密码格式
    if (!this.validatePassword(password)) {
      this.showError('密码格式不正确（至少6位，必须包含字母和数字）')
      return
    }

    // 验证两次密码是否一致
    if (password !== confirmPassword) {
      console.log('密码不一致')
      this.showError('两次输入的密码不一致')
      return
    }

    // 验证手机号格式
    if (!this.validatePhone(phone)) {
      this.showError('手机号格式不正确')
      return
    }

    // 验证邀请码（如果有）
    if (inviteCode) {
      const isValidInviteCode = await this.validateInviteCode(inviteCode)
      if (!isValidInviteCode) {
        return
      }
    }

    wx.showLoading({
      title: '注册中...'
    })

    try {
      console.log('准备调用注册云函数')
      const res = await wx.cloud.callFunction({
        name: 'userRegister',
        data: {
          username,
          password,
          confirmPassword,
          nickname,
          phone,
          inviteCode
        }
      })

      console.log('注册云函数返回结果', res)

      if (res.result && res.result.code === 0) {
        // 注册成功，保存用户信息
        console.log('注册成功，保存用户信息', res.result.data)
        const registeredUser = res.result.data; // 获取注册成功的用户信息
        wx.setStorageSync('userInfo', registeredUser)

        // ---- 开始：添加调用积分云函数逻辑 ----
        if (registeredUser && registeredUser._id) {
          console.log('准备为新用户添加注册奖励积分', registeredUser._id);
          wx.cloud.callFunction({
            name: 'operateUserPoints', // 积分操作云函数名称
            data: {
              userId: registeredUser._id, // 新用户的ID
              action: 'register_bonus',   // 操作类型：注册奖励
              pointsChange: 10,           // 修改这里：points -> pointsChange
              description: '新用户注册奖励' // 可选的描述信息
            }
          }).then(pointsRes => {
            console.log('调用operateUserPoints成功', pointsRes);
            if (pointsRes.result && pointsRes.result.success) {
              console.log('新用户注册奖励积分添加成功');
              // 可选：如果需要立即更新本地userInfo中的积分，可以在这里处理
              // 例如：
              // const updatedUserInfo = { ...registeredUser, points: (registeredUser.points || 0) + 10 };
              // wx.setStorageSync('userInfo', updatedUserInfo);
            } else {
              console.error('调用operateUserPoints成功，但操作失败或返回错误', pointsRes);
            }
          }).catch(pointsErr => {
            console.error('调用operateUserPoints失败', pointsErr);
            // 即便积分操作失败，注册流程本身也认为是成功的，所以这里只记录错误，不阻断用户流程
          });
        } else {
          console.warn('注册成功，但无法获取到用户ID，无法添加注册奖励积分。用户信息:', registeredUser);
        }
        // ---- 结束：添加调用积分云函数逻辑 ----
        
        // 如果有邀请码，完成邀请流程
        if (inviteCode && registeredUser && registeredUser._id) {
          console.log('准备完成邀请流程', { inviteCode, inviteeId: registeredUser._id });
          
          wx.showLoading({
            title: '处理邀请关系...'
          });
          
          wx.cloud.callFunction({
            name: 'completeInvitation',
            data: {
              inviteeId: registeredUser._id,
              code: inviteCode
            }
          }).then(inviteRes => {
            wx.hideLoading();
            console.log('完成邀请流程结果：', inviteRes);
            
            // 处理邀请结果
            if (inviteRes.result && inviteRes.result.success) {
              const pointsAwarded = inviteRes.result.pointsAwarded || 0;
              if (pointsAwarded > 0) {
                wx.showToast({
                  title: `邀请成功，邀请人获得${pointsAwarded}积分`,
                  icon: 'success',
                  duration: 3000
                });
              } else if (inviteRes.result.reachedDailyLimit) {
                wx.showToast({
                  title: '邀请成功，但邀请人已达每日奖励上限',
                  icon: 'none',
                  duration: 3000
                });
              }
            } else {
              console.error('邀请流程失败：', inviteRes.result ? inviteRes.result.message : '未知错误');
              // 邀请失败不影响注册流程，只显示提示
              wx.showToast({
                title: '邀请处理失败：' + (inviteRes.result ? inviteRes.result.message : '未知错误'),
                icon: 'none',
                duration: 3000
              });
            }
          }).catch(inviteErr => {
            wx.hideLoading();
            console.error('完成邀请流程异常：', inviteErr);
            wx.showToast({
              title: '邀请处理出错，请联系客服',
              icon: 'none',
              duration: 3000
            });
          });
        }
        
        wx.showToast({
          title: '注册成功',
          icon: 'success'
        })

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/user/index'
          })
        }, 1500)
      } else {
        console.error('注册失败，返回错误:', res)
        this.showError(res.result ? res.result.message : '注册失败，请重试')
      }
    } catch (e) {
      console.error('注册过程出现异常：', e)
      this.showError('注册失败，请重试：' + (e.message || '未知错误'))
    } finally {
      wx.hideLoading()
    }
  },

  // 跳转到登录页面
  goToLogin() {
    console.log('跳转到登录页面')
    wx.navigateBack()
  },

  // 处理协议同意
  handleAgreeChange(e) {
    console.log('协议同意状态改变', e.detail.value)
    this.setData({
      isAgree: e.detail.value.includes('agree')
    })
  },

  // 显示协议
  showAgreement() {
    console.log('显示用户协议')
    wx.navigateTo({
      url: '/pages/user/agreement'
    })
  },

  // 显示错误提示
  showError(message) {
    console.error('错误提示：', message)
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  }
}) 