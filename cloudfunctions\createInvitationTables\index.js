// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()

// 定义表名常量
const TABLE_NAMES = {
  INVITATION_CODES: 'invitation_codes',
  INVITATION_RECORDS: 'invitation_records',
  INVITATION_ACTIVITIES: 'invitation_activities',
  POINT_LOGS: 'point_logs',
  USERS: 'users'
}

// 检查表是否存在
async function checkTableExists(tableName) {
  try {
    await db.collection(tableName).limit(1).get()
    console.log(`表 ${tableName} 已存在`)
    return true
  } catch (e) {
    if (e.errCode === -502005) { // 表不存在的错误码
      console.log(`表 ${tableName} 不存在`)
      return false
    } else {
      console.error(`检查表 ${tableName} 时出错:`, e)
      throw e
    }
  }
}

// 创建表
async function createTable(tableName) {
  try {
    // 注意：云开发中没有直接创建表的API，表会在首次添加数据时自动创建
    // 这里我们添加一个临时文档，然后立即删除它，以此创建表
    console.log(`尝试创建表 ${tableName}`)
    
    const result = await db.createCollection(tableName)
    console.log(`表 ${tableName} 创建成功:`, result)
    return true
  } catch (e) {
    console.error(`创建表 ${tableName} 失败:`, e)
    throw e
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  console.log('开始检查和创建邀请系统所需的数据库表')
  
  const results = {}
  
  // 检查和创建所有必要的表
  const requiredTables = [
    TABLE_NAMES.INVITATION_CODES,
    TABLE_NAMES.INVITATION_RECORDS,
    TABLE_NAMES.INVITATION_ACTIVITIES,
    TABLE_NAMES.POINT_LOGS
  ]
  
  for (const tableName of requiredTables) {
    try {
      const exists = await checkTableExists(tableName)
      if (!exists) {
        await createTable(tableName)
        results[tableName] = '已创建'
      } else {
        results[tableName] = '已存在'
      }
    } catch (e) {
      results[tableName] = `错误: ${e.message}`
    }
  }
  
  // 检查users表是否存在（不创建，因为应该已经存在）
  try {
    const usersExists = await checkTableExists(TABLE_NAMES.USERS)
    results[TABLE_NAMES.USERS] = usersExists ? '已存在' : '不存在！请先创建用户表'
  } catch (e) {
    results[TABLE_NAMES.USERS] = `错误: ${e.message}`
  }
  
  return {
    success: true,
    results: results,
    message: '检查和创建表完成'
  }
} 