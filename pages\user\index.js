const app = getApp()

Page({
  data: {
    userInfo: {},
    isLoggedIn: false,
    roleText: {
      'normal': '普通用户',
      'vip': 'VIP用户',
      'admin': '管理员',
      'reviewer': '审核员'
    }
  },

  onLoad() {
    this.loadUserInfo()
  },

  onShow() {
    this.loadUserInfo()
    // 设置自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2  // 2 表示"我的"页面
      })
    }
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      // 确保角色值是字符串形式
      if (typeof userInfo.role === 'number') {
        userInfo.role = this.convertRoleToString(userInfo.role)
      }
      this.setData({
        userInfo
      })
    } else {
      // 未登录，跳转到登录页
      wx.redirectTo({
        url: '/pages/user/login'
      })
    }
  },

  // 将数字角色转换为字符串（兼容旧数据）
  convertRoleToString(roleNumber) {
    const roleMap = {
      0: 'normal',
      1: 'vip',
      2: 'admin',
      3: 'reviewer'
    }
    return roleMap[roleNumber] || 'normal'
  },

  // 跳转到我的提交
  goToSubmissions() {
    wx.navigateTo({
      url: '/pages/parking/submissions/index'
    })
  },

  // 跳转到我的收藏
  goToCollections() {
    wx.navigateTo({
      url: '/pages/parking/collections'
    })
  },

  // 新增：跳转到我的积分页面
  goToMyPoints() {
    wx.navigateTo({
      url: '/pages/points/index' // 更新为新的积分页面路径
    });
  },

  // 跳转到邀请好友页面
  goToInvitation() {
    wx.navigateTo({
      url: '/pages/invitation/index'
    });
  },

  // 新增：跳转到用户积分管理页面 (仅管理员/审核员)
  goToUserManagementPage() {
    // 双重保险，虽然WXML已经用wx:if控制了显示，但JS层面也可以加个角色判断
    if (this.data.userInfo && (this.data.userInfo.role === 'admin' || this.data.userInfo.role === 'reviewer')) {
      wx.navigateTo({
        url: '/pages/admin/userManagement/index' // 跳转到用户列表页面
      });
    } else {
      // 理论上不应该执行到这里，因为按钮是条件渲染的
      wx.showToast({
        title: '权限不足',
        icon: 'none'
      });
    }
  },

  // 跳转到管理员后台
  goToAdminPanel() {
    wx.navigateTo({
      url: '/pages/admin/index'
    })
  },

  // 跳转到审核员后台
  goToReviewerPanel() {
    wx.navigateTo({
      url: '/pages/reviewer/index'
    })
  },

  // 跳转到用户管理
  goToUserManage() {
    wx.navigateTo({
      url: '/pages/admin/userManagement'
    })
  },

  // 跳转到停车场审核
  goToSubmissionReview() {
    wx.navigateTo({
      url: '/pages/admin/submissionReview'
    })
  },

  // 复制用户ID
  copyUserId() {
    const userId = this.data.userInfo._id
    if (userId && userId !== '未知') {
      wx.setClipboardData({
        data: userId,
        success: () => {
          wx.showToast({
            title: '用户ID已复制',
            icon: 'success',
            duration: 2000
          })
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'error',
            duration: 2000
          })
        }
      })
    } else {
      wx.showToast({
        title: '用户ID无效',
        icon: 'error',
        duration: 2000
      })
    }
  },

  // 处理退出登录
  handleLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除存储的用户信息
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('authInfo')

          // 清除全局数据
          getApp().globalData.userInfo = null;

          // 跳转到登录页
          wx.redirectTo({
            url: '/pages/user/login'
          })
        }
      }
    })
  }
})