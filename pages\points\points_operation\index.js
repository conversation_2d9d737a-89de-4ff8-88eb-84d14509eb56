// 获取应用实例
const app = getApp();
const db = wx.cloud.database();

Page({
  data: {
    userId: null, // 目标用户ID
    userInfo: {}, // 用户基本信息
    currentUserInfo: null, // 当前登录用户信息
    hasPermission: false, // 是否有权限操作积分
    
    // 操作类型列表
    operationTypes: [
      { id: 'ADMIN_ADJUST', name: '管理员自定义调整', points: 0, action: 'ADMIN_ADJUST' },
      { id: 'NEW_USER_REGISTER', name: '新用户注册奖励', points: 20, action: 'ADMIN_ADD_POINTS' },
      { id: 'REFER_NEW_USER', name: '推荐新用户奖励', points: 10, action: 'ADMIN_ADD_POINTS' },
      { id: 'SUBMIT_NORMAL_PARKING', name: '提交普通停车场奖励', points: 10, action: 'ADMIN_ADD_POINTS' },
      { id: 'SUBMIT_PREMIUM_PARKING', name: '提交优质停车场奖励', points: 20, action: 'ADMIN_ADD_POINTS' },
      { id: 'REVIEW_APPROVE_BONUS', name: '审核通过额外奖励', points: 2, action: 'ADMIN_ADD_POINTS' },
      { id: 'COMMENT_NORMAL_PARKING', name: '评论普通停车场奖励', points: 3, action: 'ADMIN_ADD_POINTS' },
      { id: 'COMMENT_PREMIUM_PARKING', name: '评论优质停车场奖励', points: 5, action: 'ADMIN_ADD_POINTS' },
      { id: 'VIEW_NORMAL_PARKING', name: '查看普通停车场扣除', points: -15, action: 'ADMIN_DEDUCT_POINTS' },
      { id: 'VIEW_PREMIUM_PARKING', name: '查看优质停车场扣除', points: -30, action: 'ADMIN_DEDUCT_POINTS' }
    ],
    selectedTypeIndex: 0, // 默认选择第一个操作类型
    
    pointsToChange: '', // 自定义积分调整值
    reason: '', // 操作原因
    isSubmitting: false // 是否正在提交操作
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('[points-operation/onLoad] 页面加载，参数:', options);
    
    // 获取当前登录用户信息并检查权限
    const storedUserInfo = wx.getStorageSync('userInfo');
    if (!storedUserInfo || !storedUserInfo._id) {
      this.handleNoPermission('请先登录');
      return;
    }
    
    // 检查用户权限，只有管理员和审核员可以操作积分
    const hasPermission = storedUserInfo.role === 'admin' || storedUserInfo.role === 'reviewer';
    if (!hasPermission) {
      this.handleNoPermission('您没有权限执行此操作');
      return;
    }
    
    this.setData({
      currentUserInfo: storedUserInfo,
      hasPermission: true
    });
    
    if (options.userId) {
      this.setData({
        userId: options.userId
      });
      this.fetchUserDetails();
    } else {
      wx.showToast({
        title: '参数错误，无法加载用户',
        icon: 'none',
        duration: 2000,
        complete: () => {
          wx.navigateBack();
        }
      });
    }
  },

  /**
   * 处理无权限的情况
   */
  handleNoPermission(message) {
    console.warn(`[points-operation/handleNoPermission] ${message}`);
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000,
      complete: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    if (!this.data.userId) {
      wx.stopPullDownRefresh();
      return;
    }
    this.fetchUserDetails().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 获取用户详细信息
   */
  async fetchUserDetails() {
    console.log('[points-operation/fetchUserDetails] 尝试获取用户信息，用户ID:', this.data.userId);
    
    if (!this.data.userId) {
      console.warn('[points-operation/fetchUserDetails] 无用户ID，中止操作');
      return;
    }
    
    wx.showLoading({ title: '加载中...' });
    
    try {
      const res = await db.collection('users').doc(this.data.userId).get();
      console.log('[points-operation/fetchUserDetails] 数据库返回结果:', res);
      
      this.setData({
        userInfo: res.data
      });
    } catch (error) {
      console.error('[points-operation/fetchUserDetails] 获取用户详情失败:', error);
      wx.showToast({
        title: '加载用户信息失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 操作类型选择变化
   */
  onOperationTypeChange: function(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      selectedTypeIndex: index
    });
    
    // 如果不是自定义调整，则设置对应的积分值
    if (this.data.operationTypes[index].id !== 'ADMIN_ADJUST') {
      this.setData({
        pointsToChange: this.data.operationTypes[index].points.toString()
      });
    }
  },

  /**
   * 积分输入变化（仅在自定义调整时使用）
   */
  onPointsInputChange: function(e) {
    this.setData({
      pointsToChange: e.detail.value
    });
  },

  /**
   * 操作原因输入变化
   */
  onReasonInput: function(e) {
    this.setData({
      reason: e.detail.value
    });
  },

  /**
   * 导航到积分详情页面
   */
  navigateToPointsDetail: function() {
    if (!this.data.userId) {
      wx.showToast({
        title: '无法获取用户ID',
        icon: 'none'
      });
      return;
    }

    console.log(`[points-operation/navigateToPointsDetail] 导航到用户 ID: ${this.data.userId} 的积分详情页面`);
    
    wx.navigateTo({
      url: `/pages/points/index?targetUserId=${this.data.userId}`,
      fail: (err) => {
        console.error('[points-operation/navigateToPointsDetail] 跳转到积分详情页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 提交积分操作
   */
  async submitPointsOperation() {
    console.log('[points-operation/submitPointsOperation] 尝试提交积分操作');
    
    // 参数验证
    const selectedType = this.data.operationTypes[this.data.selectedTypeIndex];
    let pointsChange;
    
    if (selectedType.id === 'ADMIN_ADJUST') {
      // 自定义调整需要验证输入的积分值
      const pointsStr = this.data.pointsToChange.trim();
      if (pointsStr === '' || isNaN(pointsStr)) {
        wx.showToast({ title: '请输入有效的积分数值', icon: 'none' });
        return;
      }
      pointsChange = parseInt(pointsStr, 10);
      if (pointsChange === 0) {
        wx.showToast({ title: '积分为0，无需调整', icon: 'none' });
        return;
      }
    } else {
      // 使用预定义的积分值
      pointsChange = selectedType.points;
    }
    
    // 验证操作原因
    const reason = this.data.reason.trim();
    if (reason === '') {
      wx.showToast({ title: '请输入操作原因', icon: 'none' });
      return;
    }
    if (reason.length > 100) {
      wx.showToast({ title: '原因过长(最多100字)', icon: 'none' });
      return;
    }
    
    // 设置操作状态
    this.setData({ isSubmitting: true });
    wx.showLoading({ title: '处理中...' });
    
    // 确定操作类型和描述
    const actionType = selectedType.id === 'ADMIN_ADJUST' 
      ? (pointsChange > 0 ? 'ADMIN_ADD_POINTS' : 'ADMIN_DEDUCT_POINTS')
      : selectedType.action;
    
    const description = `${selectedType.name}：${reason} (${pointsChange > 0 ? '+' : ''}${pointsChange}分)`;
    
    try {
      console.log('[points-operation/submitPointsOperation] 调用云函数operateUserPoints，参数:', {
        userId: this.data.userId,
        pointsChange,
        action: actionType,
        description
      });
      
      const callRes = await wx.cloud.callFunction({
        name: 'operateUserPoints',
        data: {
          userId: this.data.userId,
          pointsChange: pointsChange,
          action: actionType,
          description: description
        }
      });
      
      console.log('[points-operation/submitPointsOperation] 云函数返回结果:', callRes);
      
      wx.hideLoading();
      
      if (callRes.result && callRes.result.success) {
        wx.showToast({
          title: '积分操作成功',
          icon: 'success'
        });
        
        // 更新当前用户积分显示
        this.setData({
          'userInfo.points': callRes.result.data.newPoints,
          reason: '', // 清空原因输入
          isSubmitting: false
        });
        
        // 如果是自定义调整，则清空积分输入
        if (selectedType.id === 'ADMIN_ADJUST') {
          this.setData({
            pointsToChange: ''
          });
        }
      } else {
        console.error('[points-operation/submitPointsOperation] 积分操作失败:', callRes.result);
        wx.showToast({
          title: callRes.result.error || '操作失败，请重试',
          icon: 'none'
        });
        this.setData({ isSubmitting: false });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('[points-operation/submitPointsOperation] 调用云函数异常:', error);
      wx.showToast({
        title: '系统错误，请稍后再试',
        icon: 'none'
      });
      this.setData({ isSubmitting: false });
    }
  }
}); 