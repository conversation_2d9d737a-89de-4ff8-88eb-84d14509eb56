/* 页面容器 */
.container {
  padding: 20rpx;
  box-sizing: border-box;
}

/* 通用区块样式 */
.section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 15rpx;
}

/* 用户信息区域 */
.user-profile {
  display: flex;
  align-items: center;
  position: relative;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  background-color: #f5f5f5;
}

.user-text-info {
  flex: 1;
}

.user-nickname {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

.user-id {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.current-points {
  font-size: 30rpx;
  color: #007aff;
  font-weight: bold;
}

/* 查看详情按钮 */
.view-details-btn {
  padding: 10rpx 20rpx;
  background-color: #f0f9ff;
  border: 1px solid #1890ff;
  border-radius: 30rpx;
  color: #1890ff;
  font-size: 24rpx;
  text-align: center;
  margin-left: 20rpx;
}

.view-details-btn:active {
  opacity: 0.8;
}

/* 操作表单区域 */
.operation-form {
  margin-top: 20rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}

/* 选择器样式 */
.picker-view {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
}

.arrow-down {
  font-size: 24rpx;
  color: #999;
}

/* 积分输入框 */
.points-input {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 30rpx;
}

/* 积分显示 */
.points-display {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 34rpx;
  font-weight: bold;
}

.points-positive {
  color: #07c160;
}

.points-negative {
  color: #fa5151;
}

/* 原因输入框 */
.reason-input {
  width: 100%;
  height: 160rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 提交按钮 */
.submit-button {
  margin-top: 40rpx;
  background-color: #007aff;
  color: white;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
}

.submit-button[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
}

/* 积分规则区域 */
.rules-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.rule-item {
  margin-bottom: 10rpx;
} 