/* 邀请码页面样式 */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1aad19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 邀请码展示区域 */
.invite-code-container {
  width: 100%;
}

.invite-code-box {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.invite-code-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.invite-code-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.invite-code-expire {
  font-size: 24rpx;
  color: #999;
}

.invite-code {
  font-size: 48rpx;
  font-weight: bold;
  color: #1aad19;
  text-align: center;
  padding: 30rpx 0;
  letter-spacing: 4rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  margin: 20rpx 0;
  background-color: #f9f9f9;
}

.invite-code-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
}

.action-btn {
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  flex: 1;
  margin: 0 10rpx;
}

.copy-btn {
  background-color: #1aad19;
  color: #fff;
}

.share-btn {
  background-color: #07c160;
  color: #fff;
}

.generate-btn {
  background-color: #1aad19;
  color: #fff;
}

/* 统计区域 */
.stats-container {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #1aad19;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 规则区域 */
.rules-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #1aad19;
  border-radius: 4rpx;
}

.rules-content {
  padding: 10rpx 0;
}

.rule-item {
  display: flex;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.rule-dot {
  color: #1aad19;
  margin-right: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.rule-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 活动区域 */
.activities-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activities-list {
  padding: 10rpx 0;
}

.activity-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.activity-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.activity-bonus {
  font-size: 26rpx;
  color: #ff6b6b;
  font-weight: bold;
}

/* 排行榜入口 */
.ranking-entry {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.arrow {
  color: #999;
  font-size: 32rpx;
}

/* 已邀请用户列表 */
.invited-users-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.invited-users-list {
  padding: 10rpx 0;
}

.invited-user-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.invited-user-item:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-nickname {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.invite-time {
  font-size: 24rpx;
  color: #999;
}

.no-data {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 分享弹窗 */
.share-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-dialog {
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.share-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.share-dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.share-dialog-close {
  font-size: 40rpx;
  color: #999;
}

.share-dialog-content {
  padding: 30rpx;
}

.share-code {
  margin-bottom: 20rpx;
}

.code-text {
  font-weight: bold;
  color: #1aad19;
  margin-left: 10rpx;
}

.share-qrcode {
  display: flex;
  justify-content: center;
  margin: 30rpx 0;
}

.share-qrcode image {
  width: 300rpx;
  height: 300rpx;
}

.share-link {
  margin-bottom: 30rpx;
  word-break: break-all;
}

.link-text {
  color: #576b95;
  margin-left: 10rpx;
}

.share-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
}

.share-btn {
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  flex: 1;
  margin: 0 10rpx;
  background-color: #1aad19;
  color: #fff;
} 