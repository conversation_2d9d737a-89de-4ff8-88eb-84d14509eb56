// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command

// 定义表名常量，避免硬编码
const TABLE_NAMES = {
  INVITATION_CODES: 'invitation_codes',
  INVITATION_RECORDS: 'invitation_records',
  INVITATION_ACTIVITIES: 'invitation_activities',
  POINT_LOGS: 'point_logs',
  USERS: 'users'
}

// 获取今日开始和结束时间
function getTodayRange() {
  const today = new Date()
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)
  return { startOfDay, endOfDay }
}

// 检查用户是否已被邀请过
async function checkUserInvited(inviteeId) {
  try {
    const result = await db.collection(TABLE_NAMES.INVITATION_RECORDS)
      .where({
        invitee_id: inviteeId,
        status: 'completed'
      })
      .count()
    
    return result.total > 0
  } catch (e) {
    console.error(`检查用户是否已被邀请过失败:`, e)
    throw new Error(`检查用户是否已被邀请过失败: ${e.message}`)
  }
}

// 检查邀请人今日邀请次数是否达到上限
async function checkInviterDailyLimit(inviterId) {
  try {
    const { startOfDay, endOfDay } = getTodayRange()
    
    const result = await db.collection(TABLE_NAMES.INVITATION_RECORDS)
      .where({
        inviter_id: inviterId,
        create_time: _.gte(startOfDay).and(_.lte(endOfDay)),
        status: 'completed',
        points_awarded: _.gt(0) // 只计算已奖励积分的记录
      })
      .count()
    
    return {
      count: result.total,
      reachedLimit: result.total >= 10 // 每日最多10次奖励
    }
  } catch (e) {
    console.error(`检查邀请人今日邀请次数失败:`, e)
    throw new Error(`检查邀请人今日邀请次数失败: ${e.message}`)
  }
}

// 检查当前是否有活跃的邀请活动
async function getActiveActivity() {
  try {
    const now = new Date()
    
    const result = await db.collection(TABLE_NAMES.INVITATION_ACTIVITIES)
      .where({
        status: 'active',
        start_time: _.lte(now),
        end_time: _.gte(now)
      })
      .orderBy('points_multiplier', 'desc') // 如果有多个活动，选择倍数最高的
      .limit(1)
      .get()
    
    return result.data.length > 0 ? result.data[0] : null
  } catch (e) {
    console.error(`检查活跃邀请活动失败:`, e)
    // 如果查询活动失败，不阻止流程，返回null
    return null
  }
}

// 检查表是否存在
async function checkTableExists(tableName) {
  try {
    await db.collection(tableName).limit(1).get()
    console.log(`表 ${tableName} 存在`)
    return true
  } catch (e) {
    console.error(`表 ${tableName} 不存在或无法访问：`, e)
    return false
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { inviteeId, code } = event
  
  console.log('完成邀请流程开始，参数：', { inviteeId, code })
  
  if (!inviteeId || !code) {
    console.error('参数不完整：', { inviteeId, code })
    return {
      success: false,
      message: '参数不完整'
    }
  }
  
  // 检查数据库表是否存在
  console.log('开始检查数据库表是否存在')
  const requiredTables = [
    TABLE_NAMES.INVITATION_CODES,
    TABLE_NAMES.INVITATION_RECORDS, 
    TABLE_NAMES.INVITATION_ACTIVITIES,
    TABLE_NAMES.POINT_LOGS,
    TABLE_NAMES.USERS
  ]
  
  const tableExistsResults = {}
  for (const tableName of requiredTables) {
    tableExistsResults[tableName] = await checkTableExists(tableName)
    if (!tableExistsResults[tableName]) {
      return {
        success: false,
        message: `数据库表 ${tableName} 不存在，请在云开发控制台创建该表`,
        missingTable: tableName
      }
    }
  }
  console.log('所有必要的数据库表都存在')
  
  // 开启数据库事务
  let transaction
  try {
    transaction = await db.startTransaction()
  } catch (e) {
    console.error('开启事务失败：', e)
    return {
      success: false,
      message: '开启事务失败：' + e.message
    }
  }
  
  try {
    // 查询邀请码
    console.log('查询邀请码：', code)
    let codeResult
    try {
      codeResult = await transaction.collection(TABLE_NAMES.INVITATION_CODES)
        .where({
          code: code,
          status: 'active'
        })
        .get()
      console.log('邀请码查询结果：', codeResult)
    } catch (e) {
      console.error('查询邀请码失败：', e)
      await transaction.rollback()
      return {
        success: false,
        message: '查询邀请码失败：' + e.message
      }
    }
    
    if (codeResult.data.length === 0) {
      await transaction.rollback()
      console.error('邀请码无效：', code)
      return {
        success: false,
        message: '邀请码无效'
      }
    }
    
    const inviteCode = codeResult.data[0]
    const inviterId = inviteCode.user_id
    
    console.log('邀请人ID：', inviterId)
    
    // 检查是否自己邀请自己
    if (inviterId === inviteeId) {
      await transaction.rollback()
      console.error('不能邀请自己：', { inviterId, inviteeId })
      return {
        success: false,
        message: '不能邀请自己'
      }
    }
    
    // 检查被邀请人是否已被邀请过
    console.log('检查被邀请人是否已被邀请过：', inviteeId)
    let alreadyInvited
    try {
      alreadyInvited = await checkUserInvited(inviteeId)
      console.log('被邀请人已被邀请过？', alreadyInvited)
    } catch (e) {
      console.error('检查被邀请人是否已被邀请过失败：', e)
      await transaction.rollback()
      return {
        success: false,
        message: '检查被邀请人是否已被邀请过失败：' + e.message
      }
    }
    
    if (alreadyInvited) {
      await transaction.rollback()
      console.error('该用户已被邀请过：', inviteeId)
      return {
        success: false,
        message: '该用户已被邀请过'
      }
    }
    
    // 检查邀请人今日邀请次数
    console.log('检查邀请人今日邀请次数：', inviterId)
    let dailyLimitInfo
    try {
      dailyLimitInfo = await checkInviterDailyLimit(inviterId)
      console.log('邀请人今日邀请次数：', dailyLimitInfo.count, '是否达到上限：', dailyLimitInfo.reachedLimit)
    } catch (e) {
      console.error('检查邀请人今日邀请次数失败：', e)
      await transaction.rollback()
      return {
        success: false,
        message: '检查邀请人今日邀请次数失败：' + e.message
      }
    }
    
    const { count: dailyCount, reachedLimit } = dailyLimitInfo
    
    // 检查是否有活跃的邀请活动
    console.log('检查是否有活跃的邀请活动')
    let activeActivity
    try {
      activeActivity = await getActiveActivity()
      console.log('活跃的邀请活动：', activeActivity)
    } catch (e) {
      console.error('检查活跃邀请活动失败：', e)
      // 活动查询失败不影响主流程，继续执行
    }
    
    const activityId = activeActivity ? activeActivity._id : null
    const pointsMultiplier = activeActivity ? activeActivity.points_multiplier : 1
    
    // 计算奖励积分
    const basePoints = 10 // 基础奖励10积分
    const pointsToAward = reachedLimit ? 0 : Math.floor(basePoints * pointsMultiplier)
    console.log('计算奖励积分：', { basePoints, pointsMultiplier, pointsToAward, reachedLimit })
    
    // 记录邀请关系
    const now = new Date()
    console.log('记录邀请关系')
    try {
      const invitationRecordData = {
        inviter_id: inviterId,
        invitee_id: inviteeId,
        code: code,
        create_time: now,
        status: 'completed',
        points_awarded: pointsToAward,
        activity_id: activityId,
        daily_count: dailyCount + 1
      }
      console.log('准备添加邀请记录：', invitationRecordData)
      
      await transaction.collection(TABLE_NAMES.INVITATION_RECORDS).add({
        data: invitationRecordData
      })
      console.log('邀请关系记录成功')
    } catch (e) {
      console.error('记录邀请关系失败：', e)
      await transaction.rollback()
      return {
        success: false,
        message: '记录邀请关系失败：' + e.message,
        error: e.toString()
      }
    }
    
    // 更新邀请码使用次数
    console.log('更新邀请码使用次数')
    try {
      await transaction.collection(TABLE_NAMES.INVITATION_CODES)
        .doc(inviteCode._id)
        .update({
          data: {
            usage_count: _.inc(1)
          }
        })
      console.log('邀请码使用次数更新成功')
    } catch (e) {
      console.error('更新邀请码使用次数失败：', e)
      await transaction.rollback()
      return {
        success: false,
        message: '更新邀请码使用次数失败：' + e.message,
        error: e.toString()
      }
    }
    
    // 如果有积分奖励，更新邀请人积分
    if (pointsToAward > 0) {
      console.log('更新邀请人积分：', { inviterId, pointsToAward })
      try {
        // 更新用户积分
        await transaction.collection(TABLE_NAMES.USERS)
          .doc(inviterId)
          .update({
            data: {
              points: _.inc(pointsToAward)
            }
          })
        console.log('用户积分更新成功')
      } catch (e) {
        console.error('更新用户积分失败：', e)
        await transaction.rollback()
        return {
          success: false,
          message: '更新用户积分失败：' + e.message,
          error: e.toString()
        }
      }
      
      console.log('记录积分变动')
      try {
        // 记录积分变动 - 使用常量表名
        const pointLogData = {
          user_id: inviterId,
          points: pointsToAward,
          action: 'invitation_reward',
          description: `邀请用户 ${inviteeId.substr(0, 8)}... 获得奖励`,
          create_time: now,
          related_id: inviteeId
        }
        console.log('准备添加积分记录：', pointLogData)
        
        await transaction.collection(TABLE_NAMES.POINT_LOGS).add({
          data: pointLogData
        })
        console.log('积分变动记录成功')
      } catch (e) {
        console.error('记录积分变动失败：', e)
        await transaction.rollback()
        return {
          success: false,
          message: '记录积分变动失败：' + e.message,
          error: e.toString()
        }
      }
    }
    
    // 提交事务
    console.log('准备提交事务')
    await transaction.commit()
    console.log('事务提交成功')
    
    return {
      success: true,
      pointsAwarded: pointsToAward,
      reachedDailyLimit: reachedLimit,
      dailyCount: dailyCount + 1,
      message: pointsToAward > 0 
        ? `邀请成功，获得${pointsToAward}积分奖励` 
        : '邀请成功，但已达每日奖励上限'
    }
  } catch (error) {
    // 回滚事务
    if (transaction) {
      await transaction.rollback()
    }
    console.error('完成邀请流程异常：', error)
    return {
      success: false,
      message: '完成邀请流程失败：' + error.message,
      error: error.toString()
    }
  }
} 