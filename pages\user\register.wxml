<view class="container">
  <view class="logo">
    <image src="/static/images/default.png" mode="aspectFit"></image>
    <text class="title">注册账号</text>
  </view>

  <view class="form">
    <view class="input-group">
      <input class="input" type="text" placeholder="请输入用户名（4-20位字母、数字或下划线）" model:value="{{username}}" />
      <input class="input" type="text" placeholder="请输入昵称（选填）" model:value="{{nickname}}" />
      <input class="input" type="password" placeholder="请输入密码（至少6位，必须包含字母和数字）" model:value="{{password}}" />
      <input class="input" type="password" placeholder="请确认密码" model:value="{{confirmPassword}}" />
      <input class="input" type="number" placeholder="请输入手机号" model:value="{{phone}}" maxlength="11" />
      <input class="input" type="text" placeholder="邀请码（选填）" model:value="{{inviteCode}}" />
    </view>

    <button class="register-btn" bindtap="handleRegister">注册</button>
    <view class="login-link" bindtap="goToLogin">已有账号？立即登录</view>
  </view>

  <!-- 用户协议 -->
  <view class="agreement">
    <checkbox-group bindchange="handleAgreeChange">
      <checkbox value="agree" checked="{{isAgree}}">我已阅读并同意</checkbox>
    </checkbox-group>
    <text class="link" bindtap="showAgreement">《用户协议和隐私政策》</text>
  </view>
</view> 