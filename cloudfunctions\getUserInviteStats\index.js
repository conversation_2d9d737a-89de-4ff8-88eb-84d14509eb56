// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 获取今日开始和结束时间
function getTodayRange() {
  const today = new Date()
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)
  return { startOfDay, endOfDay }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const userId = event.userId || wxContext.OPENID
  
  if (!userId) {
    return {
      success: false,
      message: '用户ID不能为空'
    }
  }
  
  try {
    // 获取用户邀请码
    const codeResult = await db.collection('invitation_codes')
      .where({
        user_id: userId,
        status: 'active'
      })
      .orderBy('create_time', 'desc') // 获取最新的邀请码
      .limit(1)
      .get()
    
    // 获取用户邀请记录总数
    const totalInvitesResult = await db.collection('invitation_records')
      .where({
        inviter_id: userId,
        status: 'completed'
      })
      .count()
    
    // 获取通过邀请获得的总积分
    const totalPointsResult = await db.collection('invitation_records')
      .where({
        inviter_id: userId,
        status: 'completed',
        points_awarded: _.gt(0)
      })
      .field({
        points_awarded: true
      })
      .get()
    
    // 计算总积分
    const totalPointsEarned = totalPointsResult.data.reduce((sum, record) => sum + record.points_awarded, 0)
    
    // 获取今日邀请次数
    const { startOfDay, endOfDay } = getTodayRange()
    const dailyInvitesResult = await db.collection('invitation_records')
      .where({
        inviter_id: userId,
        create_time: _.gte(startOfDay).and(_.lte(endOfDay)),
        status: 'completed',
        points_awarded: _.gt(0)
      })
      .count()
    
    // 获取已邀请用户列表（最近10个）
    const invitedUsersResult = await db.collection('invitation_records')
      .aggregate()
      .match({
        inviter_id: userId,
        status: 'completed'
      })
      .sort({
        create_time: -1
      })
      .limit(10)
      .lookup({
        from: 'users',
        localField: 'invitee_id',
        foreignField: '_id',
        as: 'invitee'
      })
      .project({
        _id: 1,
        invitee_id: 1,
        create_time: 1,
        'invitee.nickname': 1,
        'invitee.avatarUrl': 1
      })
      .end()
    
    // 处理已邀请用户数据
    const invitedUsers = invitedUsersResult.list.map(record => {
      const invitee = record.invitee[0] || {}
      return {
        _id: record.invitee_id,
        nickname: invitee.nickname || '',
        avatarUrl: invitee.avatarUrl || '',
        register_time: record.create_time ? new Date(record.create_time).toISOString().split('T')[0] : ''
      }
    })
    
    // 构建返回数据
    const inviteCode = codeResult.data.length > 0 ? codeResult.data[0] : null
    
    return {
      success: true,
      data: {
        code: inviteCode ? inviteCode.code : '',
        expireTime: inviteCode ? inviteCode.expire_time : null,
        inviteCount: totalInvitesResult.total,
        totalPointsEarned: totalPointsEarned,
        dailyInviteCount: dailyInvitesResult.total,
        invitedUsers: invitedUsers
      }
    }
  } catch (error) {
    console.error('获取用户邀请统计数据异常：', error)
    return {
      success: false,
      message: '获取数据失败：' + error.message
    }
  }
} 