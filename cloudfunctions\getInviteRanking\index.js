// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV }) // 使用当前云环境
const db = cloud.database()
const _ = db.command
const $ = db.command.aggregate

// 获取日期范围
function getDateRange(type) {
  const now = new Date()
  let startDate, endDate
  
  switch (type) {
    case 'daily': // 日榜
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
      endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
      break
    case 'weekly': // 周榜 (本周一到现在)
      const day = now.getDay() || 7 // 转换为周一为1，周日为7
      const diff = day - 1
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - diff, 0, 0, 0)
      endDate = now
      break
    case 'monthly': // 月榜 (本月1日到现在)
      startDate = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0)
      endDate = now
      break
    case 'total': // 总榜 (所有时间)
    default:
      startDate = new Date(0) // 1970-01-01
      endDate = now
      break
  }
  
  return { startDate, endDate }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { type = 'total', limit = 50 } = event
  const userId = wxContext.OPENID
  
  try {
    const { startDate, endDate } = getDateRange(type)
    
    // 聚合查询排行榜数据
    const rankingResult = await db.collection('invitation_records')
      .aggregate()
      .match({
        create_time: _.gte(startDate).and(_.lte(endDate)),
        status: 'completed'
      })
      .group({
        _id: '$inviter_id',
        inviteCount: $.sum(1)
      })
      .sort({
        inviteCount: -1
      })
      .limit(limit)
      .lookup({
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'inviter'
      })
      .project({
        user_id: '$_id',
        inviteCount: 1,
        'inviter.nickname': 1,
        'inviter.avatarUrl': 1
      })
      .end()
    
    // 处理排行榜数据
    const rankings = rankingResult.list.map((item, index) => {
      const inviter = item.inviter[0] || {}
      return {
        rank: index + 1,
        user_id: item.user_id,
        nickname: inviter.nickname || '',
        avatarUrl: inviter.avatarUrl || '',
        inviteCount: item.inviteCount
      }
    })
    
    // 获取当前用户排名
    let userRank = null
    if (userId) {
      // 查找用户在排行榜中的位置
      const userIndex = rankings.findIndex(item => item.user_id === userId)
      
      if (userIndex !== -1) {
        // 用户在前N名内
        userRank = rankings[userIndex]
      } else {
        // 用户不在前N名，单独查询用户排名
        const userInviteCountResult = await db.collection('invitation_records')
          .where({
            inviter_id: userId,
            create_time: _.gte(startDate).and(_.lte(endDate)),
            status: 'completed'
          })
          .count()
        
        if (userInviteCountResult.total > 0) {
          // 查询有多少用户的邀请数大于当前用户
          const betterThanUserResult = await db.collection('invitation_records')
            .aggregate()
            .match({
              create_time: _.gte(startDate).and(_.lte(endDate)),
              status: 'completed'
            })
            .group({
              _id: '$inviter_id',
              inviteCount: $.sum(1)
            })
            .match({
              inviteCount: _.gt(userInviteCountResult.total)
            })
            .count('higherCount')
            .end()
          
          const higherCount = betterThanUserResult.list.length > 0 ? betterThanUserResult.list[0].higherCount : 0
          
          // 获取用户信息
          const userResult = await db.collection('users')
            .doc(userId)
            .field({
              nickname: true,
              avatarUrl: true
            })
            .get()
            .catch(() => ({ data: {} }))
          
          userRank = {
            rank: higherCount + 1,
            user_id: userId,
            nickname: userResult.data.nickname || '',
            avatarUrl: userResult.data.avatarUrl || '',
            inviteCount: userInviteCountResult.total
          }
        }
      }
    }
    
    return {
      success: true,
      rankings: rankings,
      userRank: userRank,
      timeRange: {
        start: startDate,
        end: endDate,
        type: type
      }
    }
  } catch (error) {
    console.error('获取邀请排行榜异常：', error)
    return {
      success: false,
      message: '获取排行榜失败：' + error.message
    }
  }
} 