// pages/map/map.js
// 引入模块
const mapService = require('./mapService');
const mapUtil = require('./mapUtil');
const mapUIController = require('./mapUIController');
const mapEventController = require('./mapEventController');

// 页面定义
Page({

  /**
   * 页面的初始数据
   */
  data: {
    latitude: 22.665422,
    longitude: 114.042306,
    scale: 14,
    markers: [],
    loading: false,
    searchName: '',  // 搜索关键词
    radius: 5000,    // 搜索半径，默认5公里
    showType: 'all', // 显示类型：all, parking, reward
    targetAddress: '', // 目标位置地址
    targetLocation: null, // 目标位置坐标
    nearbyParking: {
      free: [],
      vip: {
        count: 0,
        preview: null
      }
    },
    filteredParking: [], // 经过筛选的停车场数据
    selectedPriceType: 'all', // 选中的价格类型: all, FREE, TIME_FREE, LOW_PRICE
    circles: [{
      latitude: 22.665422,
      longitude: 114.042306,
      radius: 5000,
      fillColor: '#1890FF15',
      strokeColor: '#1890FF66',
      strokeWidth: 1
    }],
    filters: {
      free24h: false,
      freeTime: false,
      lowPrice: false,
      truck: false
    },
    mapContext: null,  // 地图上下文
    isRegionChanging: false,  // 是否正在改变视野
    lastRegionChangeTime: 0,   // 上次视野变化时间
    cardY: 400, // px，初始收起
    cardHeight: 400, // px
    cardMinY: 100, // px，全展开
    cardMaxY: 400, // px，收起
    resultY: 950, // 初始完全收起（确保高于屏幕底部）
    resultMinY: 100, // 全展开
    resultMaxY: 950, // 完全收起
    isResultShown: false, // 是否显示结果面板
    showFullMap: false, // 默认展开搜索区域，占据1/3屏幕
    dragStarted: false, // 拖拽状态标志
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('地图页面加载')
    console.log('页面加载参数:', options)
    
    // 初始化地图上下文
    const mapContext = wx.createMapContext('map', this);
    this.setData({
      mapContext: mapContext,
      showFullMap: false // 默认展开搜索区域，占据1/3屏幕
    });
    
    // 初始化UI控制器
    this.uiController = mapUIController.init(this);
    
    // 初始化事件处理器，绑定所有事件处理函数
    try {
      console.log('开始初始化事件控制器...');
      const handlers = mapEventController.init(this);
      
      // 检查handlers是否正确初始化
      if (!handlers) {
        console.error('事件控制器初始化失败，handlers为空');
        return;
      }
      
      console.log('事件控制器初始化成功，绑定处理函数...');
      
      // 绑定所有事件处理函数
      this.onSearchInput = handlers.onSearchInput;
      this.onSearch = handlers.onSearch;
      this.onRadiusChange = handlers.onRadiusChange;
      this.onRadiusInput = handlers.onRadiusInput;
      this.onRegionChange = handlers.onRegionChange;
      this.onMarkerTap = handlers.onMarkerTap;
      this.onFilterTap = handlers.onFilterTap;
      this.onPriceTypeSelect = handlers.onPriceTypeSelect;
      this.onResultMove = handlers.onResultMove;
      this.onResultTouchStart = handlers.onResultTouchStart;
      this.onResultTouchEnd = handlers.onResultTouchEnd;
      this.onResultBarTap = handlers.onResultBarTap;
      this.onSearchInView = handlers.onSearchInView;
      this.refreshLocation = handlers.refreshLocation;
      this.onTypeChange = handlers.onTypeChange;
      this.onRequestParking = handlers.onRequestParking;
      this.onAddParking = handlers.onAddParking;
      this.onVipUpgrade = handlers.onVipUpgrade;
      this.onSelectLocation = handlers.onSelectLocation;
      this.onClearLocation = handlers.onClearLocation;
      
      console.log('事件处理函数绑定完成');
    } catch (error) {
      console.error('初始化事件控制器失败:', error);
    }
    
    // 从存储中获取用户设置的半径
    const savedRadius = wx.getStorageSync('user_search_radius');
    if (savedRadius) {
      // 如果存在保存的半径，则使用它，但不显示圆圈
      this.setData({
        radius: savedRadius,
        // 初始不显示圆圈，直到用户搜索
        circles: []
      });
      console.log('使用保存的搜索半径:', savedRadius);
    } else {
      // 不显示初始圆圈
      this.setData({
        circles: []
      });
    }
    
    // 处理可能从其他页面传入的位置参数
    if (options && options.latitude && options.longitude) {
      const lat = parseFloat(options.latitude);
      const lng = parseFloat(options.longitude);
      
      console.log('从其他页面传入的位置:', {
        latitude: lat,
        longitude: lng,
        address: options.address || ''
      });
      
      if (!isNaN(lat) && !isNaN(lng)) {
        this.setData({
          latitude: lat,
          longitude: lng,
          targetAddress: options.address || '',
          targetLocation: {
            latitude: lat,
            longitude: lng
          }
        });
      } else {
        console.error('传入的位置参数无效:', options);
      }
    }
    
    // 计算初始拖拽面板位置
    this.initResultPanelPosition();
    
    // 获取位置并加载数据
    setTimeout(() => {
      // 延迟执行定位，确保界面已完全初始化
      this.getUserLocation(true); // 初始化时加载数据
    }, 300);
  },

  /**
   * 初始化拖拽面板位置
   * 单独提取为方法，便于调试和维护
   */
  initResultPanelPosition() {
    try {
      // 获取系统信息，用于计算安全的初始位置
      let windowHeight = 800; // 默认高度
      try {
        // 使用新API获取窗口信息
        if (wx.getWindowInfo) {
          const windowInfo = wx.getWindowInfo();
          windowHeight = windowInfo.windowHeight || 800;
        } else {
          // 兼容旧版本
          const systemInfo = wx.getSystemInfoSync();
          windowHeight = systemInfo.windowHeight;
        }
      } catch (err) {
        console.error('获取窗口信息失败:', err);
      }
      
      // 计算底部导航栏高度
      const bottomNavHeight = 60; // 底部导航栏高度调整为60px，原来是100px
      
      // 拖拽栏高度 (包括其padding和视觉元素)
      const dragBarTotalHeight = 80; // 拖拽栏总高度约80px
      
      // 计算安全最大Y值，确保拖拽栏和底部导航栏始终可见
      const safeMaxY = windowHeight - bottomNavHeight - dragBarTotalHeight;
      
      // 安全地设置初始值
      this.setData({
        resultY: windowHeight * 0.5, // 初始状态下移到屏幕中间
        resultMaxY: safeMaxY, // 收起状态时露出拖拽栏高度和底部导航栏
        resultMinY: 0, // 完全展开时，Y偏移为0
        dragStarted: false // 确保初始状态动画可用
      });
      
      console.log('初始化面板位置 (保持拖拽栏可见):', {
        屏幕高度: windowHeight,
        底部导航栏高度: bottomNavHeight,
        拖拽栏高度: dragBarTotalHeight,
        初始Y值: this.data.resultY,
        最大Y值: this.data.resultMaxY,
        最小Y值: this.data.resultMinY,
        安全最大Y值: safeMaxY
      });
    } catch (e) {
      console.error('获取系统信息失败:', e);
      // 使用默认值
      this.setData({
        resultY: 500,
        resultMaxY: 450, // 确保露出拖拽栏和底部导航栏
        resultMinY: 0,
        dragStarted: false
      });
      
      console.log('使用默认面板位置:', {
        初始Y值: 500,
        最大Y值: 450,
        最小Y值: 0
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 确保地图上下文已创建
    if (!this.data.mapContext) {
      this.setData({
        mapContext: wx.createMapContext('map', this)
      });
    }
    
    // 确保resultMaxY设置正确（检查是否考虑了拖拽栏和导航栏的高度）
    try {
      // 获取系统信息
      let screenHeight = 0;
      const bottomNavHeight = 60; // 底部导航栏高度调整为60px，原来是100px
      const dragBarHeight = 80; // 拖拽栏高度约80px
      
      // 使用推荐的新API替代 wx.getSystemInfoSync()
      const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};
      screenHeight = windowInfo.windowHeight || 800;
      
      // 计算安全的最大Y值
      const safeMaxY = screenHeight - bottomNavHeight - dragBarHeight;
      
      // 如果当前设置的resultMaxY值不安全（超过了安全值），更新它
      if (this.data.resultMaxY > safeMaxY) {
        this.setData({
          resultMaxY: safeMaxY
        });
        console.log('onReady: 修正面板最大Y值为安全值:', safeMaxY);
      }
    } catch (e) {
      console.error('onReady: 检查resultMaxY时出错:', e);
    }
    
    // 初始化时隐藏面板，但保持拖拽栏可见
    this.setData({
      isResultShown: true // 保持为true，确保至少显示拖拽栏
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 设置自定义tabBar的选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0  // 0 表示"发现"页面
      })
    }
    this.loadNearbyData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshLocation()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '发现附近的好车位',
      path: '/pages/map/map'
    }
  },

  // 获取用户位置
  async getUserLocation(loadData = true) {
    // 防止重复调用 
    if (this._isGettingLocation) {
      console.log('正在获取位置中，请勿重复调用');
      return;
    }
    
    this._isGettingLocation = true;
    
    wx.showLoading({
      title: '定位中...',
      mask: true
    })

    try {
      // 先检查权限
      const hasAuth = await this.checkLocationAuth()
      if (!hasAuth) {
        wx.hideLoading()
        this._isGettingLocation = false;
        return
      }

      // 获取保存的半径，但不显示圆圈
      const savedRadius = wx.getStorageSync('user_search_radius') || this.data.radius;

      const res = await wx.getLocation({
        type: 'gcj02',
        isHighAccuracy: true,
        highAccuracyExpireTime: 3000
      })
      
      console.log('获取到用户位置:', res)
      
      // 防止触发连续的地图视图更新
      this._isUpdatingRegion = true;
      
      // 更新位置，但不显示圆圈
      this.setData({
        latitude: res.latitude,
        longitude: res.longitude,
        scale: 16,
        radius: savedRadius, // 保存半径值，但不显示圆圈
        circles: [], // 不显示圆圈
        // 更新最后视野变化时间，避免触发循环更新
        lastRegionChangeTime: Date.now()
      })
      
      // 延迟释放地图更新锁
      setTimeout(() => {
        this._isUpdatingRegion = false;
      }, 300);

      // 仅在需要时加载停车场数据
      if (loadData) {
        this.loadNearbyData()
      } else {
        console.log('仅更新位置，不加载数据')
      }
    } catch (err) {
      console.error('获取位置失败：', err)
      wx.showToast({
        title: '获取位置失败，请检查定位权限',
        icon: 'none',
        duration: 2000
      })
      // 即使定位失败也尝试加载数据，但仅在需要加载数据时
      if (loadData) {
        this.loadNearbyData()
      }
    } finally {
      wx.hideLoading()
      // 释放位置获取锁
      setTimeout(() => {
        this._isGettingLocation = false;
      }, 300);
    }
  },

  // 检查定位权限
  async checkLocationAuth() {
    try {
      const setting = await wx.getSetting()
      if (!setting.authSetting['scope.userLocation']) {
        // 如果没有定位权限，则申请权限
        try {
          await wx.authorize({
            scope: 'scope.userLocation'
          })
          return true
        } catch (err) {
          // 用户拒绝了权限申请，引导用户去设置页面开启
          wx.showModal({
            title: '需要定位权限',
            content: '请允许小程序使用定位权限，以便为您提供更好的服务',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting()
              }
            }
          })
          return false
        }
      }
      return true
    } catch (err) {
      console.error('检查权限失败：', err)
      return false
    }
  },

  /**
   * 加载附近数据
   */
  async loadNearbyData() {
    if (this.data.loading) {
      console.log('正在加载中，跳过重复加载')
      return
    }
    
    this.setData({ loading: true })
    wx.showLoading({
      title: '搜索中...',
      mask: true
    })
    
    try {
      // 准备查询参数
      const options = {
        latitude: this.data.latitude,
        longitude: this.data.longitude,
        radius: this.data.radius,
        searchName: this.data.searchName
      }
      
      // 加载停车场数据
      const parkingResult = await mapService.loadNearbyParking(options)
      
      console.log('加载到的停车场数据:', parkingResult);
      
      // 检查返回数据的结构
      if (!parkingResult || !parkingResult.nearbyParking) {
        console.error('返回的停车场数据结构不正确:', parkingResult);
        wx.showToast({
          title: '数据格式错误',
          icon: 'none',
          duration: 2000
        });
        this.setData({ loading: false });
        wx.hideLoading();
        return;
      }
      
      // 将所有停车场数据合并到一个数组
      const allParking = [...(parkingResult.nearbyParking.free || [])];
      
      console.log('合并后的原始停车场数据数量:', allParking.length);
      
      // 确保数据格式正确
      const validParking = allParking.map((item, index) => {
        // 创建新对象而不是修改原始对象，避免引用问题
        const normalizedItem = {...item};
        
        // 确保每个项目都有一个唯一的_id
        if (!normalizedItem._id) {
          normalizedItem._id = 'parking_' + Date.now() + '_' + index;
          console.log(`为第${index+1}个项目添加ID:`, normalizedItem._id);
        }
        
        // 确保有基本信息
        if (!normalizedItem.name) {
          normalizedItem.name = `未命名停车场${index+1}`;
          console.log(`为第${index+1}个项目添加名称:`, normalizedItem.name);
        }
        
        // 确保有距离信息
        if (typeof normalizedItem.distance !== 'number') {
          normalizedItem.distance = Math.round(Math.random() * 500 + 100);
          console.log(`为第${index+1}个项目添加距离:`, normalizedItem.distance);
        }
        
        return normalizedItem;
      });
      
      console.log('处理后的停车场数据:', validParking, '总数:', validParking.length);
      
      // 确保有测试数据 - 当实际数据不足时添加模拟数据
      let finalParking = [...validParking];
      if (finalParking.length < 2) {
        // 在开发环境中添加测试数据
        // 微信小程序没有process.env，使用其他方式判断环境
        const isDevEnv = __wxConfig && __wxConfig.envVersion !== 'release';
        if (isDevEnv) {
          console.log('添加测试数据以确保滚动功能正常工作');
          
          // 克隆第一个项目并修改一些属性用于测试
          if (finalParking.length > 0) {
            const testItem = {...finalParking[0]};
            testItem._id = 'test_' + Date.now();
            testItem.name = '测试停车场';
            testItem.distance = testItem.distance + 100;
            finalParking.push(testItem);
            
            // 再添加一个测试项目
            const testItem2 = {...finalParking[0]};
            testItem2._id = 'test2_' + Date.now();
            testItem2.name = '另一个测试停车场';
            testItem2.distance = testItem.distance + 150;
            finalParking.push(testItem2);
          } else {
            // 如果没有任何数据，创建模拟数据
            finalParking = [
              {
                _id: 'mock_1',
                name: '模拟停车场1',
                distance: 200,
                available_spots: 5,
                capacity: 20,
                price: '免费',
                businessHours: '24小时',
                tags: ['free', 'public'],
                price_type: 'FREE'
              },
              {
                _id: 'mock_2',
                name: '模拟停车场2',
                distance: 350,
                available_spots: 2,
                capacity: 15,
                price: '2元/小时',
                businessHours: '8:00-22:00',
                tags: ['time_limited'],
                price_type: 'TIME_FREE'
              }
            ];
          }
          console.log('添加测试数据后总数:', finalParking.length);
        }
      }
      
      // 应用价格类型筛选
      let filteredParking = finalParking;
      if (this.data.selectedPriceType !== 'all' && finalParking.length > 0) {
        filteredParking = finalParking.filter(item => {
          // 如果当前项有price_type属性，则直接比较
          if (item.price_type) {
            return item.price_type === this.data.selectedPriceType;
          }
          // 如果没有price_type，但有tags，则检查tags中是否包含相关类型
          else if (Array.isArray(item.tags)) {
            return item.tags.some(tag => {
              if (this.data.selectedPriceType === 'FREE' && tag.includes('free')) return true;
              if (this.data.selectedPriceType === 'TIME_FREE' && tag.includes('time_limited')) return true;
              if (this.data.selectedPriceType === 'LOW_PRICE' && tag.includes('low_price')) return true;
              return false;
            });
          }
          // 如果没有price_type和tags，默认显示在"全部"分类中
          return this.data.selectedPriceType === 'all';
        });
      }
      
      console.log('筛选后的停车场数据:', filteredParking, '总数:', filteredParking.length);
      
      // 更新停车场数据
      this.setData({
        markers: parkingResult.markers || [],
        nearbyParking: {
          free: finalParking,
          vip: parkingResult.nearbyParking.vip || { count: 0 }
        },
        filteredParking: filteredParking
      }, () => {
        // 在数据更新完成后，延迟调整面板位置
        setTimeout(() => {
          if (filteredParking.length > 0) {
            // 如果有数据，确保面板足够展开以显示数据
            this.adjustPanelForContent();
            console.log('调整面板位置以展示', filteredParking.length, '个停车场');
          }
        }, 200);
      });
      
    } catch (err) {
      console.error('加载数据失败:', err)
      wx.showToast({
        title: '加载失败，请稍后重试',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({ loading: false })
      wx.hideLoading()
    }
  },

  // 获取标签文本
  getTagText(tag) {
    return mapUtil.getTagText(tag)
  },
  
  // 获取设施文本
  getFacilityText(facility) {
    const facilityMap = {
      'underground': '地下车库',
      'fast': '快充',
      'slow': '慢充',
      'camera': '监控',
      'guard': '保安',
      'restroom': '卫生间',
      'power': '水电',
      'truck': '可停货车',
      'campervan': '可停房车'
    }
    return facilityMap[facility] || facility
  },

  // 搜索按钮点击
  onSearch() {
    console.log('搜索按钮被点击')
    
    // 先清除之前可能存在的延时操作
    if (this._searchResultTimer) {
      clearTimeout(this._searchResultTimer)
    }
    
    // 设置锁防止重复操作
    this._isSearching = true;
    
    // 先加载数据
    this.loadNearbyData()
    
    // 设置面板样式，使其可见
    try {
      // 获取系统信息，计算屏幕高度的2/3位置（留出1/3给结果面板）
      const systemInfo = wx.getSystemInfoSync();
      const screenHeight = systemInfo.windowHeight;
      // 计算2/3屏幕高度的位置，让结果面板占据底部1/3
      const resultPanelY = Math.floor(screenHeight * 2/3);
      
      console.log('计算结果面板位置:', {
        screenHeight: screenHeight,
        resultPanelY: resultPanelY
      });
      
      // 强制立即显示结果面板
      this.setData({ 
        resultY: resultPanelY,
        isResultShown: true
      });
    } catch(err) {
      console.error('计算面板位置失败:', err)
      // 备用方案：直接设置一个固定位置
      this.setData({ resultY: 500, isResultShown: true })
    }
    
    // 确保数据加载完成后面板仍然可见
    this._searchResultTimer = setTimeout(() => {
      try {
        const systemInfo = wx.getSystemInfoSync();
        const screenHeight = systemInfo.windowHeight;
        const resultPanelY = Math.floor(screenHeight * 2/3);
        
        // 确保面板保持可见
        this.setData({ 
          resultY: resultPanelY,
          isResultShown: true
        });
      } catch(e) {
        console.log('延时调整面板位置失败:', e);
      }
      
      // 释放搜索锁
      this._isSearching = false;
    }, 800);
  },
  
  // 价格类型选择
  onPriceTypeSelect(e) {
    const priceType = e.currentTarget.dataset.type
    this.setData({
      selectedPriceType: priceType
    })
    
    // 根据价格类型筛选数据
    let filteredParking = []
    const allParking = [...(this.data.nearbyParking.free || [])]
    
    // 确保有数据且为数组
    if (!Array.isArray(allParking) || allParking.length === 0) {
      console.log('没有可用的停车场数据进行筛选');
      this.setData({ filteredParking: [] });
      return;
    }
    
    if (priceType === 'all') {
      filteredParking = allParking
    } else {
      // 使用更灵活的匹配方式
      filteredParking = allParking.filter(item => {
        // 如果当前项有price_type属性，则直接比较
        if (item.price_type) {
          return item.price_type === priceType;
        }
        // 如果没有price_type，但有tags，则检查tags中是否包含相关类型
        else if (Array.isArray(item.tags)) {
          return item.tags.some(tag => {
            if (priceType === 'FREE' && tag.includes('free')) return true;
            if (priceType === 'TIME_FREE' && tag.includes('time_limited')) return true;
            if (priceType === 'LOW_PRICE' && tag.includes('low_price')) return true;
            return false;
          });
        }
        return false;
      });
    }
    
    console.log(`价格类型筛选(${priceType}):`, filteredParking.length, '条结果');
    
    this.setData({ filteredParking }, () => {
      // 如果筛选后有数据，稍微调整面板位置以便更好地查看数据
      if (filteredParking.length > 0) {
        this.adjustPanelForContent();
      }
    });
  },

  // 发布停车场信息
  onAddParking() {
    wx.navigateTo({
      url: '/pages/parking/submit'
    })
  },

  // 添加切换搜索区域显示/隐藏的功能
  // 引入搜索区域切换方法
  toggleSearchArea() {
    this.setData({
      showFullMap: !this.data.showFullMap
    });
    
    // 如果开始搜索或隐藏搜索区域，则调整地图视图
    if (this.data.showFullMap) {
      // 延迟调整地图，确保UI渲染完成
      setTimeout(() => {
        try {
          // 更新地图上下文
          if (this.data.mapContext) {
            this.data.mapContext.includePoints({
              points: [{
                latitude: this.data.latitude,
                longitude: this.data.longitude
              }],
              padding: [80, 80, 80, 80]
            });
          }
        } catch (e) {
          console.error('调整地图视图失败:', e);
        }
      }, 300);
    }
  },

  // 处理内容区域滚动
  onContentScroll(e) {
    // 记录当前正在滚动，避免与面板拖拽冲突
    this._isContentScrolling = true;
    
    // 记录最后滚动时间
    this._lastScrollTime = Date.now();
    
    // 300ms后重置滚动状态，避免长时间锁定
    clearTimeout(this._scrollTimer);
    this._scrollTimer = setTimeout(() => {
      // 只有当最后滚动时间超过300ms才重置滚动状态
      if (Date.now() - this._lastScrollTime >= 300) {
        this._isContentScrolling = false;
      }
    }, 350);
    
    console.log('内容区域滚动中，滚动距离:', e.detail.scrollTop);
  },

  // 根据内容调整面板位置
  adjustPanelForContent() {
    try {
      // 获取系统信息
      const windowInfo = wx.getWindowInfo ? wx.getWindowInfo() : {};
      const screenHeight = windowInfo.windowHeight || 800;
      
      // 计算适合的面板位置 - 显示约98%的屏幕内容，进一步增大显示区域，确保所有内容可见
      const contentDisplayRatio = 0.98; // 从0.95增加到0.98，尽可能显示更多内容
      const targetY = Math.floor(screenHeight * (1 - contentDisplayRatio));
      
      // 确保不超出边界
      const safeTargetY = Math.max(
        Math.min(targetY, this.data.resultMaxY),
        this.data.resultMinY
      );
      
      // 更新面板位置
      this.setData({
        resultY: safeTargetY,
        isResultShown: true
      });
      
      console.log('调整面板位置以显示更多内容:', {
        屏幕高度: screenHeight,
        目标位置: targetY,
        安全位置: safeTargetY,
        显示比例: contentDisplayRatio,
        列表项数量: this.data.filteredParking ? this.data.filteredParking.length : 0
      });

      // 获取并打印当前结果列表中项目的数量
      if (this.data.filteredParking && this.data.filteredParking.length > 0) {
        console.log(`当前停车场列表共有 ${this.data.filteredParking.length} 项`);
        
        // 延迟短暂时间后检查列表项渲染情况并滚动到顶部
        setTimeout(() => {
          try {
            // 获取列表容器尺寸和位置信息
            const query = wx.createSelectorQuery();
            query.select('.parking-items').boundingClientRect();
            query.selectAll('.parking-item').boundingClientRect();
            query.exec((res) => {
              if (res && res[0] && res[1]) {
                const containerRect = res[0];
                const itemRects = res[1];
                
                console.log(`列表容器高度: ${containerRect.height}px`);
                console.log(`检测到 ${itemRects.length} 个列表项被渲染`);
                
                // 记录每个列表项的位置以便调试
                itemRects.forEach((item, index) => {
                  console.log(`列表项 ${index+1}: 位置 Y=${item.top}, 高度=${item.height}px`);
                });
                
                // 确保滚动到顶部，让用户从第一项开始浏览
                try {
                  const scrollView = wx.createSelectorQuery().select('.result-list-content');
                  scrollView.node().exec((nodeRes) => {
                    if (nodeRes && nodeRes[0] && nodeRes[0].node) {
                      nodeRes[0].node.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                      });
                    }
                  });
                } catch (e) {
                  console.log('滚动到顶部失败，尝试替代方法');
                }
              }
            });
          } catch (e) {
            console.error('检查列表项渲染失败:', e);
          }
          
          // 在面板调整后，执行更强烈的滚动提示动画，确保用户知道内容可滚动
          try {
            // 创建一个定时循环，使内容短暂滚动呈现波浪效果，然后回到顶部
            let scrollStep = 0;
            const maxScrollStep = 30;
            const scrollInterval = setInterval(() => {
              try {
                if (scrollStep >= 2) { // 减少到2次，避免过多干扰
                  clearInterval(scrollInterval);
                  
                  // 最后回到顶部
                  setTimeout(() => {
                    wx.createSelectorQuery()
                      .select('.result-list-content')
                      .node()
                      .exec((res) => {
                        if (res && res[0] && res[0].node) {
                          res[0].node.scrollTo({
                            top: 0,
                            behavior: 'smooth'
                          });
                        }
                      });
                  }, 300);
                  return;
                }
                
                // 计算当前滚动量，形成波浪效果
                const scrollAmount = Math.sin(scrollStep * Math.PI / 2) * maxScrollStep;
                
                // 尝试使用节点滚动API
                wx.createSelectorQuery()
                  .select('.result-list-content')
                  .node()
                  .exec((res) => {
                    if (res && res[0] && res[0].node) {
                      res[0].node.scrollTo({
                        top: scrollAmount,
                        behavior: 'smooth'
                      });
                    }
                  });
                
                scrollStep++;
              } catch (e) {
                console.error('自动滚动提示出错:', e);
                clearInterval(scrollInterval);
              }
            }, 600);
          } catch (scrollError) {
            console.log('提示滚动失败:', scrollError);
          }
        }, 800); // 延长等待时间，确保DOM已完全渲染
      }
    } catch (error) {
      console.error('调整面板位置失败:', error);
    }
  },
})