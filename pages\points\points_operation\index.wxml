<view class="container">
  <!-- 用户基本信息 -->
  <view class="user-profile section">
    <image class="user-avatar" src="{{userInfo.avatarUrl || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
    <view class="user-text-info">
      <view class="user-nickname">{{userInfo.nickName || '未命名用户'}}</view>
      <view class="user-id">ID: {{userInfo._id}}</view>
      <view class="current-points">当前积分: {{userInfo.points || 0}}</view>
    </view>
    <view class="view-details-btn" bindtap="navigateToPointsDetail">
      <text>查看详情</text>
    </view>
  </view>

  <!-- 积分操作区域 -->
  <view class="points-operation section">
    <view class="section-title">积分操作</view>
    <view class="operation-form">
      <!-- 操作类型选择 -->
      <view class="form-item">
        <view class="form-label">操作类型</view>
        <picker bindchange="onOperationTypeChange" value="{{selectedTypeIndex}}" range="{{operationTypes}}" range-key="name">
          <view class="picker-view">
            {{operationTypes[selectedTypeIndex].name}}
            <view class="arrow-down">▼</view>
          </view>
        </picker>
      </view>

      <!-- 积分值显示/输入 -->
      <view class="form-item">
        <view class="form-label">积分变化</view>
        <block wx:if="{{operationTypes[selectedTypeIndex].id === 'ADMIN_ADJUST'}}">
          <input class="points-input" type="number" placeholder="输入积分值 (正数增加，负数减少)" value="{{pointsToChange}}" bindinput="onPointsInputChange" />
        </block>
        <block wx:else>
          <view class="points-display {{operationTypes[selectedTypeIndex].points >= 0 ? 'points-positive' : 'points-negative'}}">
            {{operationTypes[selectedTypeIndex].points >= 0 ? '+' : ''}}{{operationTypes[selectedTypeIndex].points}}
          </view>
        </block>
      </view>

      <!-- 操作原因输入 -->
      <view class="form-item">
        <view class="form-label">操作原因</view>
        <textarea class="reason-input" placeholder="请输入操作原因 (必填，最多100字)" value="{{reason}}" bindinput="onReasonInput" maxlength="100"></textarea>
        <view class="char-count">{{reason.length}}/100</view>
      </view>

      <!-- 提交按钮 -->
      <button class="submit-button" bindtap="submitPointsOperation" disabled="{{isSubmitting}}">
        {{isSubmitting ? '处理中...' : '确认操作'}}
      </button>
    </view>
  </view>

  <!-- 积分规则提示 -->
  <view class="points-rules section">
    <view class="section-title">积分规则说明</view>
    <view class="rules-content">
      <view class="rule-item">• 新用户注册：+20分（一次性奖励）</view>
      <view class="rule-item">• 推荐新用户：+10分（每日上限5次）</view>
      <view class="rule-item">• 提交普通停车场：+10分</view>
      <view class="rule-item">• 提交优质停车场：+20分</view>
      <view class="rule-item">• 评论普通停车场：+3分（每日上限10次）</view>
      <view class="rule-item">• 评论优质停车场：+5分（每日上限10次）</view>
      <view class="rule-item">• 查看普通停车场：-15分</view>
      <view class="rule-item">• 查看优质停车场：-30分（每日上限5次）</view>
      <view class="rule-item">• 每日积分获取上限：100分</view>
    </view>
  </view>
</view> 