<view class="container">
  <view class="header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <text class="title">邀请排行榜</text>
  </view>

  <!-- 榜单切换 -->
  <view class="tab-container">
    <view class="tab-item {{currentTab === 'daily' ? 'active' : ''}}" bindtap="switchTab" data-tab="daily">
      <text>日榜</text>
    </view>
    <view class="tab-item {{currentTab === 'weekly' ? 'active' : ''}}" bindtap="switchTab" data-tab="weekly">
      <text>周榜</text>
    </view>
    <view class="tab-item {{currentTab === 'monthly' ? 'active' : ''}}" bindtap="switchTab" data-tab="monthly">
      <text>月榜</text>
    </view>
    <view class="tab-item {{currentTab === 'total' ? 'active' : ''}}" bindtap="switchTab" data-tab="total">
      <text>总榜</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>

  <!-- 排行榜内容 -->
  <view class="ranking-container" wx:else>
    <!-- 我的排名 -->
    <view class="my-rank-container" wx:if="{{userRank}}">
      <view class="my-rank-title">我的排名</view>
      <view class="rank-item my-rank">
        <view class="rank-position">{{formatRank(userRank.rank)}}</view>
        <image class="user-avatar" src="{{userRank.avatarUrl || '/static/images/default-avatar.png'}}"></image>
        <view class="user-info">
          <text class="user-nickname">{{userRank.nickname || '用户' + userRank.user_id.substring(0, 4)}}</text>
          <text class="user-id">ID: {{userRank.user_id.substring(0, 8)}}...</text>
        </view>
        <view class="invite-count">{{userRank.inviteCount}}人</view>
      </view>
    </view>

    <!-- 排行榜列表 -->
    <view class="ranking-list">
      <view class="ranking-title">排行榜</view>
      <block wx:if="{{rankings.length > 0}}">
        <view class="rank-item {{item.user_id === userRank.user_id ? 'highlight' : ''}}" wx:for="{{rankings}}" wx:key="user_id">
          <view class="rank-position">{{formatRank(index + 1)}}</view>
          <image class="user-avatar" src="{{item.avatarUrl || '/static/images/default-avatar.png'}}"></image>
          <view class="user-info">
            <text class="user-nickname">{{item.nickname || '用户' + item.user_id.substring(0, 4)}}</text>
            <text class="user-id">ID: {{item.user_id.substring(0, 8)}}...</text>
          </view>
          <view class="invite-count">{{item.inviteCount}}人</view>
        </view>
      </block>
      <view class="no-data" wx:else>
        <text>暂无排行数据</text>
      </view>
    </view>

    <!-- 排行榜规则 -->
    <view class="rules-container">
      <view class="rules-title">排行榜规则</view>
      <view class="rules-content">
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">日榜：统计当日00:00至23:59的邀请人数</text>
        </view>
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">周榜：统计本周一至周日的邀请人数</text>
        </view>
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">月榜：统计本月1日至月末的邀请人数</text>
        </view>
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">总榜：统计所有时间的累计邀请人数</text>
        </view>
        <view class="rule-item">
          <text class="rule-dot">•</text>
          <text class="rule-text">排行榜每小时更新一次数据</text>
        </view>
      </view>
    </view>
  </view>
</view> 