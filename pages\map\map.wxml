<!--pages/map/map.wxml-->
<view class="container">
  <!-- 搜索区域 -->
  <view class="search-container {{showFullMap ? 'compact' : ''}}">
    <!-- 切换按钮 -->
    <view class="toggle-search" bindtap="toggleSearchArea">
      <text class="toggle-icon">{{showFullMap ? '↓' : '↑'}}</text>
    </view>
    
    <view class="search-content {{showFullMap ? 'hidden' : ''}}">
      <view class="search-bar">
        <view class="location-picker" bindtap="onSelectLocation">
          <text class="location-icon">📍</text>
          <text wx:if="{{targetAddress}}" class="location-text">{{targetAddress}}</text>
          <text wx:else class="location-placeholder">点击选择搜索地点...</text>
          <view wx:if="{{targetAddress}}" class="clear-location" catchtap="onClearLocation">×</view>
        </view>
        <button class="search-btn" bindtap="onSearch">搜索</button>
      </view>
      
      <!-- 主要筛选标签和半径控制放在同一行 -->
      <view class="filter-section">
        <view class="compact-controls">
          <view class="type-filter">
            <view class="type-tag {{showType === 'all' ? 'active' : ''}}" bindtap="onTypeChange" data-type="all">
              <text>全部</text>
            </view>
            <view class="type-tag {{showType === 'parking' ? 'active' : ''}}" bindtap="onTypeChange" data-type="parking">
              <text>好车位</text>
            </view>
            <view class="type-tag {{showType === 'reward' ? 'active' : ''}}" bindtap="onTypeChange" data-type="reward">
              <text>悬赏</text>
            </view>
          </view>
          
          <!-- 搜索半径 - 紧凑版 -->
          <view class="radius-control-compact">
            <view class="radius-input-group">
              <text>半径:</text>
              <input 
                class="radius-input"
                type="digit"
                value="{{radius/1000}}"
                bindblur="onRadiusInput"
              />
              <text>km</text>
            </view>
          </view>
        </view>
        
        <!-- 子筛选标签 -->
        <scroll-view scroll-x class="sub-filter" enhanced show-scrollbar="{{false}}">
          <view class="filter-tags">
            <view class="filter-tag {{filters.free24h ? 'active' : ''}}" bindtap="onFilterTap" data-type="free24h">
              24h免费
            </view>
            <view class="filter-tag {{filters.freeTime ? 'active' : ''}}" bindtap="onFilterTap" data-type="freeTime">
              夜间免费
            </view>
            <view class="filter-tag {{filters.lowPrice ? 'active' : ''}}" bindtap="onFilterTap" data-type="lowPrice">
              低价包月
            </view>
            <view class="filter-tag {{filters.truck ? 'active' : ''}}" bindtap="onFilterTap" data-type="truck">
              可停货车
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 搜索半径滑块 -->
      <slider 
        min="100" 
        max="50000" 
        value="{{radius}}" 
        bindchange="onRadiusChange"
        block-color="#1890FF"
        show-value="{{false}}"
        class="radius-slider"
      />
    </view>
  </view>

  <map
    id="map"
    class="map {{showFullMap ? 'full' : ''}}"
    latitude="{{latitude}}"
    longitude="{{longitude}}"
    scale="{{scale}}"
    markers="{{markers}}"
    circles="{{circles}}"
    show-location
    bindmarkertap="onMarkerTap"
    bindregionchange="onRegionChange"
  >
    <!-- 搜索范围提示 -->
    <cover-view class="radius-indicator" wx:if="{{circles && circles.length > 0}}">
      <cover-view class="indicator-text">搜索半径: {{radius/1000}}km</cover-view>
    </cover-view>
  </map>

  <!-- 可拖拽全屏上拉/下拉列表 -->
  <view
    class="result-list-movable {{isResultShown ? 'visible' : ''}} {{dragStarted ? 'no-transition' : ''}}"
    style="transform: translateY({{resultY}}px); position: fixed; width: 100%; bottom: 0; left:0; right:0; z-index:101; display: block;"
  >
    <!-- 拖拽栏 - 只有此处添加触摸事件，确保只有拖拽栏可以拖动整个面板 -->
    <view class="drag-bar" 
      catchtouchstart="onResultTouchStart"
      catchtouchmove="onResultMove"
      catchtouchend="onResultTouchEnd"
      catchtouchcancel="onResultTouchEnd"
      catchtap="onResultBarTap"></view>
    
    <!-- 位置指示点 -->
    <view class="position-dots">
      <view class="position-dot {{resultY < (resultMinY + 20) ? 'active' : ''}}"></view>
      <view class="position-dot {{Math.abs(resultY - (resultMinY + (resultMaxY - resultMinY) * 0.25)) < 20 ? 'active' : ''}}"></view>
      <view class="position-dot {{Math.abs(resultY - ((resultMaxY + resultMinY) / 2)) < 20 ? 'active' : ''}}"></view>
      <view class="position-dot {{Math.abs(resultY - (resultMinY + (resultMaxY - resultMinY) * 0.75)) < 20 ? 'active' : ''}}"></view>
      <view class="position-dot {{resultY > (resultMaxY - 20) ? 'active' : ''}}"></view>
    </view>
    
    <!-- 内容区域 - 允许滚动 -->
    <scroll-view 
      class="result-list-content scrollable-area" 
      style="padding-bottom:{{resultY < 300 ? '50rpx' : '30rpx'}}"
      scroll-y="{{true}}" 
      enable-flex="{{true}}"
      enhanced="{{true}}"
      bounces="{{true}}"
      show-scrollbar="{{true}}"
      always-bounce-vertical="{{true}}"
      enable-back-to-top="{{true}}"
      scroll-anchoring="{{true}}"
      refresher-enabled="{{false}}"
      fastdeceleration="{{true}}"
      enable-passive="{{true}}"
      bindscroll="onContentScroll"
    >
      <!-- 滚动提示 - 更加明显和直观 -->
      <view class="scroll-tip" style="background: linear-gradient(to right, rgba(255, 107, 0, 0.1), rgba(255, 107, 0, 0.2), rgba(255, 107, 0, 0.1)); padding: 16rpx 0; margin: 10rpx 0 16rpx;">
        <view>↓ 上下滑动查看全部{{filteredParking.length}}个停车场 ↓</view>
        <view style="font-size: 22rpx; opacity: 0.8; margin-top: 4rpx;">已为您找到{{filteredParking.length}}个停车场</view>
      </view>
      
      <!-- 停车场列表项目 - 添加明显的序号标识 -->
      <view class="section parking-list">
        <view class="section-title">
          <text>停车场</text>
          <text class="count" wx:if="{{filteredParking && filteredParking.length}}">{{filteredParking.length}}个</text>
          <text class="count" wx:else>0个</text>
        </view>
        
        <!-- 价格类型筛选器 -->
        <scroll-view scroll-x class="price-filter" enhanced show-scrollbar="{{false}}">
          <view class="price-tags">
            <view class="price-tag {{selectedPriceType === 'all' ? 'active' : ''}}" bindtap="onPriceTypeSelect" data-type="all">
              全部价格
            </view>
            <view class="price-tag {{selectedPriceType === 'FREE' ? 'active' : ''}}" 
              bindtap="onPriceTypeSelect" 
              data-type="FREE"
              style="color: #07C160; background-color: rgba(7, 193, 96, 0.1)"
            >
              免费停车
            </view>
            <view class="price-tag {{selectedPriceType === 'TIME_FREE' ? 'active' : ''}}" 
              bindtap="onPriceTypeSelect" 
              data-type="TIME_FREE"
              style="color: #FF9500; background-color: rgba(255, 149, 0, 0.1)"
            >
              限时免费
            </view>
            <view class="price-tag {{selectedPriceType === 'LOW_PRICE' ? 'active' : ''}}" 
              bindtap="onPriceTypeSelect" 
              data-type="LOW_PRICE"
              style="color: #1890FF; background-color: rgba(24, 144, 255, 0.1)"
            >
              低价收费
            </view>
          </view>
        </scroll-view>
        
        <!-- 停车场列表 -->
        <view class="parking-items">
          <block wx:if="{{filteredParking && filteredParking.length > 0}}">
            <view class="parking-item" 
              wx:for="{{filteredParking}}" 
              wx:key="_id"
              bindtap="onMarkerTap"
              data-id="{{item._id}}"
            >
              <!-- 添加项目序号标识 -->
              <view class="item-index">{{index + 1}}</view>
              
              <view class="available-spots {{item.available_spots < 5 ? (item.available_spots <= 0 ? 'full' : 'limited') : ''}}">
                {{item.available_spots > 0 ? '空位: ' + item.available_spots : '已满'}}
              </view>
              <view class="parking-name">{{item.name || '未命名停车场'}}</view>
              <view class="parking-info">
                <text class="distance">{{item.distance}}米</text>
                <text class="spots">{{item.capacity || 0}}个车位</text>
              </view>
              <view class="parking-detail">
                <view class="detail-item">
                  <text class="detail-label">营业时间：</text>
                  <text class="detail-value">{{item.businessHours || '24小时'}}</text>
                </view>
                <view class="detail-item">
                  <text class="detail-label">价格：</text>
                  <text class="detail-value">{{item.price || '免费'}}</text>
                  <view class="expand-icon">〉</view>
                </view>
              </view>
              <view class="parking-tags">
                <text class="tag" 
                  wx:for="{{item.tags || []}}" 
                  wx:for-item="tag"
                  wx:key="*this"
                  data-type="{{tag.indexOf('free') >= 0 ? 'free' : (tag.indexOf('discount') >= 0 ? 'discount' : 'facility')}}"
                >{{getTagText(tag)}}</text>
                <text class="tag" 
                  wx:for="{{item.facilities || []}}" 
                  wx:for-item="facility"
                  wx:key="*this"
                  data-type="facility"
                >{{getFacilityText(facility)}}</text>
              </view>
            </view>
          </block>
          <view wx:else class="empty-tip">
            暂无符合条件的停车场，请调整筛选条件
          </view>
        </view>
        
        <!-- 在列表底部添加滚动结束提示，让用户知道已经滚动到底部 -->
        <view wx:if="{{filteredParking && filteredParking.length > 1}}" class="scroll-end-tip">
          ———— 全部数据加载完成 ————
        </view>
        
        <!-- 添加底部安全区域，确保底部内容不被导航栏遮挡 -->
        <view class="bottom-safe-area"></view>
        
        <!-- 添加额外的底部安全间距，解决在特定设备上的显示问题 -->
        <view class="extra-bottom-space"></view>
      </view>

      <!-- 悬赏列表 -->
      <view class="section reward-list" wx:if="{{showType !== 'parking' && nearbyRewards && nearbyRewards.length > 0}}">
        <view class="section-title">
          <text>附近的悬赏</text>
          <text class="count">{{nearbyRewards.length}}个</text>
        </view>
        <view class="reward-items">
          <view class="reward-item" 
            wx:for="{{nearbyRewards}}" 
            wx:key="_id"
            bindtap="onRewardTap"
            data-id="{{item._id}}"
          >
            <view class="reward-header">
              <view class="reward-info">
                <text class="reward-amount">¥{{item.reward}}</text>
                <text class="reward-tag">悬赏金</text>
              </view>
              <text class="reward-distance">{{item.distance}}米</text>
            </view>
            <view class="reward-content">
              <view class="reward-desc">
                <text class="car-type">{{item.carInfo.type}}</text>
                <text class="dot">·</text>
                <text class="duration">{{item.parkingPreference.duration.start}} 至 {{item.parkingPreference.duration.end}}</text>
              </view>
              <view class="reward-tags">
                <text class="tag" wx:for="{{item.parkingPreference.type}}" wx:key="*this">{{item}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- VIP专享车位 -->
      <view class="section vip-section" wx:if="{{showType !== 'reward' && nearbyParking.vip.count > 0}}">
        <view class="section-title">
          <text>VIP专享车位</text>
          <text class="count">{{nearbyParking.vip.count}}个</text>
        </view>
        
        <view class="vip-preview" wx:if="{{nearbyParking.vip.preview}}">
          <view class="preview-content">
            <view class="preview-title">{{nearbyParking.vip.preview.name}}</view>
            <view class="preview-desc">查看附近{{nearbyParking.vip.count}}个VIP专享车位，包含室内车位和全天候保安服务</view>
            <view class="parking-tags">
              <text class="tag" wx:for="{{nearbyParking.vip.preview.tags}}" wx:key="*this">{{getTagText(item)}}</text>
            </view>
          </view>
          <view class="unlock-btn" bindtap="onVipUpgrade">立即解锁</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 操作按钮组 -->
  <view class="action-buttons">
    <button class="action-btn refresh" bindtap="refreshLocation">
      <icon type="refresh" size="20" color="#07C160"/>
      <text>定位</text>
    </button>
    <button class="action-btn reward" bindtap="onRequestParking">
      <image class="reward-icon" src="/static/images/reward.png" mode="aspectFit"/>
      <view class="btn-text">
        <text class="primary">悬赏</text>
        <text class="secondary">好车位</text>
      </view>
    </button>
    <button class="action-btn add" bindtap="onAddParking">
      <icon type="add" size="20" color="#07C160"/>
      <view class="btn-text">
        <text class="primary">发布</text>
        <text class="secondary">好车位</text>
      </view>
    </button>
  </view>
</view>